import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { AuthRepository } from "./auth.respository";
import {
  RegisterDto,
  LoginDto,
  AuthResponseDto,
  UserProfileDto,
} from "./auth.dto";
import { User } from "../user/user.model";
import { Session } from "./session.entity";
import { ApiError } from "../../utils/ApiError";
import { env } from "../../config/environment";
import { BaseService } from "../../base/BaseService";

export class AuthService extends BaseService {
  protected serviceName = "AuthService";
  private authRepository: AuthRepository;

  constructor() {
    super();
    this.authRepository = new AuthRepository();
  }

  async register(
    registerData: RegisterDto,
    ipAddress?: string,
    userAgent?: string
  ): Promise<AuthResponseDto> {
    try {
      const existingUser = await this.authRepository.findUserByEmail(
        registerData.email
      );
      if (existingUser) {
        throw ApiError.conflict("User with this email already exists");
      }

      const hashedPassword = await this.hashPassword(registerData.password);
      const userData = {
        ...registerData,
        password: hashedPassword,
      };

      const user = await this.authRepository.createUser(userData);

      const token = this.generateJWT(user.id);
      const expiresAt = this.getTokenExpirationDate();

      await this.authRepository.createSession({
        userId: user.id,
        token,
        expiresAt,
        ipAddress,
        userAgent,
      });

      return {
        user: this.mapUserToProfile(user),
        token,
        message: "User registered successfully",
      };
    } catch (error) {
      throw error;
    }
  }

  async login(
    loginData: LoginDto,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ user: UserProfileDto; token: string; message: string }> {
    try {
      const user = await this.authRepository.findUserByEmail(loginData.email);
      if (!user) {
        throw ApiError.unauthorized("Invalid email or password");
      }

      if (!user.isActive) {
        throw ApiError.forbidden("Account is deactivated");
      }

      const isPasswordValid = await this.comparePassword(
        loginData.password,
        user.password
      );

      if (!isPasswordValid) {
        throw ApiError.unauthorized("Invalid email or password");
      }

      const token = this.generateJWT(user.id);
      const expiresAt = this.getTokenExpirationDate();

      await this.authRepository.createSession({
        userId: user.id,
        token,
        expiresAt,
        ipAddress,
        userAgent,
      });

      return {
        user: this.mapUserToProfile(user),
        token,
        message: "Login successful",
      };
    } catch (error) {
      throw error;
    }
  }

  async logout(token: string): Promise<{ message: string }> {
    try {
      const session = await this.authRepository.findSessionByToken(token);
      if (session) {
        await this.authRepository.deactivateSession(session.id);
      }

      return { message: "Logout successful" };
    } catch (error) {
      throw error;
    }
  }

  async validateToken(
    token: string
  ): Promise<{ user: UserProfileDto; session: Session }> {
    try {
      const decoded = jwt.verify(token, env.jwt.secret) as { userId: string };

      // Find session in database
      const session = await this.authRepository.findSessionByToken(token);
      if (!session || !session.isValid()) {
        throw new ApiError(401, "Invalid or expired session");
      }

      return {
        user: this.mapUserToProfile(session.user),
        session,
      };
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new ApiError(401, "Invalid token");
      }
      throw error;
    }
  }

  async getUserProfile(userId: string): Promise<UserProfileDto> {
    try {
      const user = await this.authRepository.findUserById(userId);
      if (!user) {
        throw new ApiError(404, "User not found");
      }

      return this.mapUserToProfile(user);
    } catch (error) {
      throw error;
    }
  }

  async logoutAllSessions(userId: string): Promise<{ message: string }> {
    try {
      await this.authRepository.deactivateAllUserSessions(userId);
      return { message: "All sessions logged out successfully" };
    } catch (error) {
      throw error;
    }
  }

  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  }

  private async comparePassword(
    password: string,
    hashedPassword: string
  ): Promise<boolean> {
    return await bcrypt.compare(password, hashedPassword);
  }

  private generateJWT(userId: string): string {
    const payload = { userId };
    const secret = env.jwt.secret as string;

    return jwt.sign(payload, secret, {
      expiresIn: env.jwt.expiresIn,
    } as any);
  }

  private getTokenExpirationDate(): Date {
    const expiresIn = env.jwt.expiresIn;
    const now = new Date();

    // Parse the expiresIn string (e.g., "7d", "24h", "60m")
    const match = expiresIn.match(/^(\d+)([dhm])$/);
    if (!match) {
      // Default to 7 days if format is invalid
      return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    }

    const [, value, unit] = match;
    const numValue = parseInt(value, 10);

    switch (unit) {
      case "d":
        return new Date(now.getTime() + numValue * 24 * 60 * 60 * 1000);
      case "h":
        return new Date(now.getTime() + numValue * 60 * 60 * 1000);
      case "m":
        return new Date(now.getTime() + numValue * 60 * 1000);
      default:
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    }
  }

  private mapUserToProfile(user: User): UserProfileDto {
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      fullName: user.fullName,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}
