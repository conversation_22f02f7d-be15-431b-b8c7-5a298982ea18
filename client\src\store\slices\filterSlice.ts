import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { FilterState } from "../../types";
import { Category } from "../../features/menu/types/menuTypes";

const initialState: FilterState = {
  selectedCategory: "All",
  searchQuery: "",
};

const filterSlice = createSlice({
  name: "filter",
  initialState,
  reducers: {
    setSelectedCategory: (state, action: PayloadAction<Category>) => {
      state.selectedCategory = action.payload;
    },

    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },

    clearFilters: (state) => {
      state.selectedCategory = "All";
      state.searchQuery = "";
    },
  },
});

export const {
  setSelectedCategory,
  setSearchQuery,
  clearFilters,
} = filterSlice.actions;

export default filterSlice.reducer;

// Selectors
export const selectSelectedCategory = (state: { filter: FilterState }) => state.filter.selectedCategory;
export const selectSearchQuery = (state: { filter: FilterState }) => state.filter.searchQuery;
