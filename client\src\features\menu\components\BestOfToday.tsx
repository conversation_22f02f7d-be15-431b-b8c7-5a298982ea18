import { getBestOfTodayProducts } from "../../../data/products";
import { BestOfTodayItem } from "./BestOfTodayItem";

export const BestOfToday: React.FC = () => {
  const bestOfTodayProducts = getBestOfTodayProducts();

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Best of Today</h2>
      <div>
        {bestOfTodayProducts.map((item) => (
          <BestOfTodayItem key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};
