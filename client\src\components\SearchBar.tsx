import { SearchIcon } from "../assets/icons/component/SearchIcon";
import { useAppDispatch, useAppSelector } from "../hooks/redux";
import { selectSearchQuery, setSearchQuery } from "../store/slices/filterSlice";

export const SearchBar: React.FC = () => {
  const dispatch = useAppDispatch();
  const searchQuery = useAppSelector(selectSearchQuery);

  return (
    <div className="p-4">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <SearchIcon />
        </div>
        <input
          type="text"
          placeholder="Search"
          value={searchQuery}
          onChange={(e) => dispatch(setSearchQuery(e.target.value))}
          className="w-full bg-primary-100 border border-transparent rounded-lg py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-primary-500"
        />
      </div>
    </div>
  );
};
