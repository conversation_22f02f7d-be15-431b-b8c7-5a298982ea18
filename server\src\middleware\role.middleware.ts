import { Request, Response, NextFunction } from "express";
import { ApiError } from "../utils/ApiError";
import { UserRole, hasPermission } from "../types/roles.enum";

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
        restaurantId?: string;
      };
    }
  }
}

/**
 * Middleware to check if user has required role(s)
 */
export function requireRole(...allowedRoles: UserRole[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw ApiError.unauthorized("Authentication required");
      }

      const userRole = req.user.role;
      
      if (!allowedRoles.includes(userRole)) {
        throw ApiError.forbidden(
          `Access denied. Required role(s): ${allowedRoles.join(", ")}`
        );
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Middleware to check if user has required permission
 */
export function requirePermission(permission: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw ApiError.unauthorized("Authentication required");
      }

      const userRole = req.user.role;
      
      if (!hasPermission(userRole, permission)) {
        throw ApiError.forbidden(
          `Access denied. Required permission: ${permission}`
        );
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Middleware to check if user is admin
 */
export const requireAdmin = requireRole(UserRole.ADMIN);

/**
 * Middleware to check if user is restaurant owner or admin
 */
export const requireOwnerOrAdmin = requireRole(UserRole.RESTAURANT_OWNER, UserRole.ADMIN);

/**
 * Middleware to check if user is staff (owner, waiter, or admin)
 */
export const requireStaff = requireRole(
  UserRole.ADMIN,
  UserRole.RESTAURANT_OWNER,
  UserRole.WAITER
);

/**
 * Middleware to check if user can access restaurant data
 * - Admins can access any restaurant
 * - Restaurant owners can access their own restaurant
 * - Waiters can access their assigned restaurant
 */
export function requireRestaurantAccess(req: Request, res: Response, next: NextFunction) {
  try {
    if (!req.user) {
      throw ApiError.unauthorized("Authentication required");
    }

    const { role, restaurantId } = req.user;
    const requestedRestaurantId = req.params.restaurantId || req.body.restaurantId;

    // Admins can access any restaurant
    if (role === UserRole.ADMIN) {
      return next();
    }

    // Restaurant owners and waiters can only access their assigned restaurant
    if ([UserRole.RESTAURANT_OWNER, UserRole.WAITER].includes(role)) {
      if (!restaurantId) {
        throw ApiError.forbidden("User is not assigned to any restaurant");
      }

      if (requestedRestaurantId && requestedRestaurantId !== restaurantId) {
        throw ApiError.forbidden("Access denied to this restaurant");
      }

      return next();
    }

    // Clients cannot access restaurant management endpoints
    throw ApiError.forbidden("Access denied");
  } catch (error) {
    next(error);
  }
}

/**
 * Middleware to check if user can manage orders
 * - Admins can manage any order
 * - Restaurant owners can manage orders in their restaurant
 * - Waiters can manage orders in their restaurant
 * - Clients can only view their own orders
 */
export function requireOrderAccess(action: 'view' | 'manage' = 'view') {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw ApiError.unauthorized("Authentication required");
      }

      const { role, id: userId, restaurantId } = req.user;

      // Admins can do anything
      if (role === UserRole.ADMIN) {
        return next();
      }

      // For management actions
      if (action === 'manage') {
        if ([UserRole.RESTAURANT_OWNER, UserRole.WAITER].includes(role)) {
          if (!restaurantId) {
            throw ApiError.forbidden("User is not assigned to any restaurant");
          }
          return next();
        }
        throw ApiError.forbidden("Insufficient permissions to manage orders");
      }

      // For view actions
      if (role === UserRole.CLIENT) {
        // Clients can only view their own orders
        const requestedCustomerId = req.params.customerId || req.query.customerId;
        if (requestedCustomerId && requestedCustomerId !== userId) {
          throw ApiError.forbidden("Access denied to other customer's orders");
        }
        return next();
      }

      // Staff can view orders in their restaurant
      if ([UserRole.RESTAURANT_OWNER, UserRole.WAITER].includes(role)) {
        if (!restaurantId) {
          throw ApiError.forbidden("User is not assigned to any restaurant");
        }
        return next();
      }

      throw ApiError.forbidden("Access denied");
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Middleware to check if user owns the resource or is admin
 */
export function requireOwnership(userIdField: string = 'userId') {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw ApiError.unauthorized("Authentication required");
      }

      const { role, id: userId } = req.user;

      // Admins can access any resource
      if (role === UserRole.ADMIN) {
        return next();
      }

      // Check ownership
      const resourceUserId = req.params[userIdField] || req.body[userIdField];
      if (resourceUserId !== userId) {
        throw ApiError.forbidden("Access denied to this resource");
      }

      next();
    } catch (error) {
      next(error);
    }
  };
}
