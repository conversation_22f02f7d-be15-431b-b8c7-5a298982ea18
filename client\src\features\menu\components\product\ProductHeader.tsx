import { useNavigate } from "react-router-dom";

import { ArrowLeftIcon } from "../../../../assets/icons/component/ArrowLeftIcon";
import { HeartIcon } from "../../../../assets/icons/component/HeartIcon";

export default function ProductHeader({
  setIsFavorite,
  isFavorite,
}: {
  setIsFavorite: (isFavorite: boolean) => void;
  isFavorite: boolean;
}) {
  const navigate = useNavigate();

  return (
    <div className="flex justify-between items-center p-4 bg-white">
      <button
        onClick={() => navigate("/")}
        className="p-2 rounded-full hover:bg-secondary-100 transition-colors"
      >
        <ArrowLeftIcon />
      </button>
      <h1 className="text-lg font-semibold text-secondary-800">
        Product Details
      </h1>
      <button
        onClick={() => setIsFavorite(!isFavorite)}
        className={`p-2 rounded-full transition-colors ${
          isFavorite
            ? "text-red-500 bg-red-50"
            : "text-secondary-400 hover:bg-secondary-100"
        }`}
      >
        <HeartIcon />
      </button>
    </div>
  );
}
