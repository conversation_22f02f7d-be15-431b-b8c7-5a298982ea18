import { Request, Response, NextFunction } from "express";
import { ApiResponse } from "@/utils/ApiResponse";
import logger from "@/config/logger";

export abstract class BaseController {
  protected abstract controllerName: string;

  protected sendResponse<T>(
    res: Response,
    statusCode: number,
    data?: T,
    message?: string
  ): void {
    const response = new ApiResponse(true, data, message);
    res.status(statusCode).json(response);
  }

  protected sendError(
    res: Response,
    statusCode: number,
    message: string,
    error?: any
  ): void {
    const response = new ApiResponse(false, null, message, error);
    res.status(statusCode).json(response);
  }

  protected asyncHandler = (
    fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
  ) => {
    return (req: Request, res: Response, next: NextFunction) => {
      Promise.resolve(fn(req, res, next)).catch((error) => {
        logger.error(`${this.controllerName} error:`, error);
        next(error);
      });
    };
  };
}
