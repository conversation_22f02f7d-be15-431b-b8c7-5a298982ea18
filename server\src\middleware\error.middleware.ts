import { Request, Response, NextFunction } from "express";
import { ApiError } from "../utils/ApiError";
import { env } from "../config/environment";
import logger from "../config/logger";

interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    path: string;
    method: string;
    statusCode: number;
    stack?: string;
  };
  requestId?: string;
}

export class ErrorMiddleware {
  /**
   * Global error handler middleware
   */
  static handle = (
    error: any,
    req: Request,
    res: Response,
    next: NextFunction
  ): void => {
    // Generate unique request ID for tracking
    const requestId = ErrorMiddleware.generateRequestId();

    // Log the error
    ErrorMiddleware.logError(error, req, requestId);

    // Determine if it's an operational error
    const isOperational = error instanceof ApiError && error.isOperational;

    // Create structured error response
    const errorResponse = ErrorMiddleware.createErrorResponse(
      error,
      req,
      requestId,
      isOperational
    );

    // Send response
    res.status(errorResponse.error.statusCode).json(errorResponse);
  };

  /**
   * Handle 404 Not Found errors
   */
  static notFound = (req: Request, res: Response, next: NextFunction): void => {
    const error = new ApiError(
      404,
      `Route ${req.originalUrl} not found`,
      undefined,
      true
    );
    next(error);
  };

  /**
   * Create structured error response
   */
  private static createErrorResponse(
    error: any,
    req: Request,
    requestId: string,
    isOperational: boolean
  ): ErrorResponse {
    const statusCode = ErrorMiddleware.getStatusCode(error);
    const errorCode = ErrorMiddleware.getErrorCode(error, statusCode);
    const message = ErrorMiddleware.getErrorMessage(error, statusCode);

    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        code: errorCode,
        message,
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        method: req.method,
        statusCode,
      },
      requestId,
    };

    // Add details for validation errors
    if (error.errors && Array.isArray(error.errors)) {
      errorResponse.error.details = error.errors;
    }

    // Add stack trace in development for operational errors
    if (env.nodeEnv === "development" && (isOperational || statusCode >= 500)) {
      errorResponse.error.stack = error.stack;
    }

    return errorResponse;
  }

  /**
   * Get HTTP status code from error
   */
  private static getStatusCode(error: any): number {
    if (error instanceof ApiError) {
      return error.statusCode;
    }

    // Handle specific error types
    if (error.name === "ValidationError") return 400;
    if (error.name === "CastError") return 400;
    if (error.name === "JsonWebTokenError") return 401;
    if (error.name === "TokenExpiredError") return 401;
    if (error.code === 11000) return 409; // MongoDB duplicate key
    if (error.code === "23505") return 409; // PostgreSQL unique violation
    if (error.code === "23503") return 400; // PostgreSQL foreign key violation

    // Default to 500 for unknown errors
    return 500;
  }

  /**
   * Get error code for categorization
   */
  private static getErrorCode(error: any, statusCode: number): string {
    if (error instanceof ApiError && error.code) {
      return error.code;
    }

    // Generate error codes based on status and type
    switch (statusCode) {
      case 400:
        if (error.name === "ValidationError") return "VALIDATION_ERROR";
        if (error.name === "CastError") return "INVALID_DATA_FORMAT";
        if (error.code === "23503") return "FOREIGN_KEY_VIOLATION";
        return "BAD_REQUEST";
      case 401:
        if (error.name === "JsonWebTokenError") return "INVALID_TOKEN";
        if (error.name === "TokenExpiredError") return "TOKEN_EXPIRED";
        return "UNAUTHORIZED";
      case 403:
        return "FORBIDDEN";
      case 404:
        return "NOT_FOUND";
      case 409:
        if (error.code === 11000 || error.code === "23505")
          return "DUPLICATE_ENTRY";
        return "CONFLICT";
      case 422:
        return "UNPROCESSABLE_ENTITY";
      case 429:
        return "TOO_MANY_REQUESTS";
      case 500:
        return "INTERNAL_SERVER_ERROR";
      case 502:
        return "BAD_GATEWAY";
      case 503:
        return "SERVICE_UNAVAILABLE";
      case 504:
        return "GATEWAY_TIMEOUT";
      default:
        return `HTTP_${statusCode}`;
    }
  }

  /**
   * Get user-friendly error message
   */
  private static getErrorMessage(error: any, statusCode: number): string {
    if (error instanceof ApiError) {
      return error.message;
    }

    // Handle specific error types with user-friendly messages
    switch (error.name) {
      case "ValidationError":
        return "The provided data is invalid. Please check your input.";
      case "CastError":
        return "Invalid data format provided.";
      case "JsonWebTokenError":
        return "Invalid authentication token.";
      case "TokenExpiredError":
        return "Authentication token has expired.";
    }

    // Handle database errors
    if (error.code === 11000 || error.code === "23505") {
      return "A record with this information already exists.";
    }
    if (error.code === "23503") {
      return "Referenced record does not exist.";
    }

    // Default messages based on status code
    switch (statusCode) {
      case 400:
        return "Bad request. Please check your input.";
      case 401:
        return "Authentication required.";
      case 403:
        return "Access denied.";
      case 404:
        return "The requested resource was not found.";
      case 409:
        return "Conflict with existing data.";
      case 422:
        return "The request data could not be processed.";
      case 429:
        return "Too many requests. Please try again later.";
      case 500:
        return "An internal server error occurred.";
      case 502:
        return "Bad gateway error.";
      case 503:
        return "Service temporarily unavailable.";
      case 504:
        return "Gateway timeout.";
      default:
        return error.message || "An unexpected error occurred.";
    }
  }

  /**
   * Generate unique request ID for tracking
   */
  private static generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log error with context
   */
  private static logError(error: any, req: Request, requestId: string): void {
    const logData = {
      requestId,
      method: req.method,
      url: req.originalUrl,
      userAgent: req.get("User-Agent"),
      ip: req.ip,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        statusCode: error.statusCode || 500,
      },
    };

    if (error.statusCode && error.statusCode < 500) {
      logger.warn("Client Error", logData);
    } else {
      logger.error("Server Error", logData);
    }
  }
}
