import { MigrationInterface, QueryRunner, Table } from "typeorm";

export class CreateUserAndSessionTables1754923810374
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create users table
    await queryRunner.createTable(
      new Table({
        name: "users",
        columns: [
          {
            name: "id",
            type: "uuid",
            isPrimary: true,
            generationStrategy: "uuid",
            default: "gen_random_uuid()",
          },
          {
            name: "email",
            type: "varchar",
            length: "255",
            isUnique: true,
            isNullable: false,
          },
          {
            name: "password",
            type: "varchar",
            length: "255",
            isNullable: false,
          },
          {
            name: "firstName",
            type: "varchar",
            length: "100",
            isNullable: false,
          },
          {
            name: "lastName",
            type: "varchar",
            length: "100",
            isNullable: false,
          },
          {
            name: "isActive",
            type: "boolean",
            default: true,
            isNullable: false,
          },
          {
            name: "createdAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            isNullable: false,
          },
          {
            name: "updatedAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            onUpdate: "CURRENT_TIMESTAMP",
            isNullable: false,
          },
        ],
        indices: [
          {
            name: "IDX_USER_EMAIL",
            columnNames: ["email"],
            isUnique: true,
          },
        ],
      }),
      true
    );

    // Create sessions table
    await queryRunner.createTable(
      new Table({
        name: "sessions",
        columns: [
          {
            name: "id",
            type: "uuid",
            isPrimary: true,
            generationStrategy: "uuid",
            default: "gen_random_uuid()",
          },
          {
            name: "userId",
            type: "uuid",
            isNullable: false,
          },
          {
            name: "token",
            type: "text",
            isUnique: true,
            isNullable: false,
          },
          {
            name: "expiresAt",
            type: "timestamp",
            isNullable: false,
          },
          {
            name: "isActive",
            type: "boolean",
            default: true,
            isNullable: false,
          },
          {
            name: "ipAddress",
            type: "varchar",
            length: "45",
            isNullable: true,
          },
          {
            name: "userAgent",
            type: "text",
            isNullable: true,
          },
          {
            name: "createdAt",
            type: "timestamp",
            default: "CURRENT_TIMESTAMP",
            isNullable: false,
          },
        ],
        indices: [
          {
            name: "IDX_SESSION_TOKEN",
            columnNames: ["token"],
            isUnique: true,
          },
          {
            name: "IDX_SESSION_USER_ACTIVE",
            columnNames: ["userId", "isActive"],
          },
        ],
      }),
      true
    );

    // Create foreign key constraint
    await queryRunner.createForeignKey(
      "sessions",
      new (require("typeorm").TableForeignKey)({
        columnNames: ["userId"],
        referencedColumnNames: ["id"],
        referencedTableName: "users",
        onDelete: "CASCADE",
        name: "FK_SESSION_USER",
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraint
    await queryRunner.dropForeignKey("sessions", "FK_SESSION_USER");

    // Drop tables
    await queryRunner.dropTable("sessions");
    await queryRunner.dropTable("users");
  }
}
