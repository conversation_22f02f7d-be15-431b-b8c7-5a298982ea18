import React, { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useAppSelector, useAppDispatch } from "../hooks/redux";
import { addToCart, selectCartItemById } from "../store/slices/cartSlice";
import { getProductById } from "../data/products";

// Icons
const ArrowLeftIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
  </svg>
);

const PlusIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
  </svg>
);

const MinusIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
  </svg>
);

const ProductDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [quantity, setQuantity] = useState(1);

  const product = id ? getProductById(parseInt(id)) : null;
  const cartItem = useAppSelector((state) => selectCartItemById(state, product?.id || 0));

  if (!product) {
    return (
      <div className="bg-secondary-50 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-secondary-800 mb-4">Product Not Found</h2>
          <button
            onClick={() => navigate("/")}
            className="bg-primary-500 text-white px-6 py-2 rounded-lg hover:bg-primary-600 transition-colors"
          >
            Go Back Home
          </button>
        </div>
      </div>
    );
  }

  const handleAddToCart = () => {
    dispatch(addToCart({ product, quantity }));
  };

  return (
    <div className="bg-secondary-50 min-h-screen font-sans">
      <div className="max-w-md mx-auto bg-white shadow-lg">
        {/* Header */}
        <div className="flex justify-between items-center p-4 bg-white">
          <button
            onClick={() => navigate("/")}
            className="p-2 rounded-full hover:bg-secondary-100 transition-colors"
          >
            <ArrowLeftIcon />
          </button>
          <h1 className="text-lg font-semibold text-secondary-800">Product Details</h1>
          <div></div>
        </div>

        {/* Product Image */}
        <div className="px-4">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-64 rounded-xl object-cover"
          />
        </div>

        {/* Product Info */}
        <div className="p-4">
          <div className="flex justify-between items-start mb-2">
            <div>
              <h2 className="text-2xl font-bold text-secondary-800">{product.name}</h2>
              <p className="text-secondary-500">{product.category}</p>
            </div>
            <p className="text-2xl font-bold text-primary-600">{product.price} DT</p>
          </div>

          {/* Description */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-secondary-800 mb-2">Description</h3>
            <p className="text-secondary-600 leading-relaxed">{product.description}</p>
          </div>

          {/* Ingredients */}
          {product.ingredients && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-secondary-800 mb-2">Ingredients</h3>
              <div className="flex flex-wrap gap-2">
                {product.ingredients.map((ingredient, index) => (
                  <span
                    key={index}
                    className="bg-secondary-100 text-secondary-700 px-3 py-1 rounded-full text-sm"
                  >
                    {ingredient}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Quantity and Add to Cart */}
          <div className="flex items-center justify-between bg-white border-t pt-4">
            <div className="flex items-center space-x-4">
              <span className="text-secondary-600 font-medium">Quantity:</span>
              <div className="flex items-center space-x-2 bg-secondary-100 rounded-full p-1">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="p-2 rounded-full bg-white text-secondary-600 hover:bg-secondary-50 transition-colors"
                >
                  <MinusIcon />
                </button>
                <span className="font-bold w-8 text-center">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="p-2 rounded-full bg-white text-primary-500 hover:bg-primary-50 transition-colors"
                >
                  <PlusIcon />
                </button>
              </div>
            </div>
            <button 
              onClick={handleAddToCart}
              className="bg-primary-500 text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary-600 transition-colors flex items-center space-x-2"
            >
              <span>Add to Cart</span>
              <span className="bg-primary-600 px-2 py-1 rounded-full text-sm">
                {(product.price * quantity).toFixed(0)} DT
              </span>
            </button>
          </div>

          {/* Cart Status */}
          {cartItem && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-800 text-sm font-medium">
                ✓ {cartItem.quantity} item(s) in cart
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
