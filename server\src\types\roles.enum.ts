/**
 * User roles in the restaurant management system
 */
export enum UserRole {
  ADMIN = 'admin',
  RESTAURANT_OWNER = 'restaurant_owner', 
  WAITER = 'waiter',
  CLIENT = 'client'
}

/**
 * Role permissions mapping
 */
export const RolePermissions = {
  [UserRole.ADMIN]: [
    'manage_all_restaurants',
    'manage_all_users',
    'view_all_orders',
    'manage_system_settings'
  ],
  [UserRole.RESTAURANT_OWNER]: [
    'manage_own_restaurant',
    'manage_restaurant_staff',
    'manage_products',
    'manage_categories',
    'view_restaurant_orders',
    'manage_tables',
    'view_analytics'
  ],
  [UserRole.WAITER]: [
    'view_products',
    'manage_orders',
    'update_order_status',
    'view_tables',
    'take_orders'
  ],
  [UserRole.CLIENT]: [
    'view_products',
    'create_orders',
    'view_own_orders',
    'rate_products'
  ]
} as const;

/**
 * Check if a role has a specific permission
 */
export function hasPermission(role: UserRole, permission: string): boolean {
  return RolePermissions[role]?.includes(permission as any) || false;
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role: UserRole): readonly string[] {
  return RolePermissions[role] || [];
}
