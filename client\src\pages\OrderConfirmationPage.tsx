import React from "react";
import { useNavigate } from "react-router-dom";

const OrderConfirmationPage: React.FC = () => {
  const navigate = useNavigate();

  const handleViewStatusOrder = () => {
    navigate("/order-status");
  };

  const handleBackToHome = () => {
    navigate("/");
  };

  return (
    <div className="bg-secondary-50 min-h-screen font-sans">
      <div className="max-w-md mx-auto bg-white shadow-lg min-h-screen flex flex-col">
        {/* Main Content */}
        <div className="flex-1 flex flex-col items-center justify-center px-8 py-12">
          {/* Logo */}
          <div className="mb-8">
            <div className="relative">
              <div className="text-center">Logo</div>
            </div>
          </div>
          <h1 className="text-2xl font-bold text-secondary-800 mb-4 text-center">
            Order Confirmed
          </h1>
          <div className="text-center mb-8 px-4">
            <p className="text-secondary-600 leading-relaxed">
              Thank you for your order! Your food is almost ready.
            </p>
            <p className="text-secondary-600 mt-2 font-medium">
              Enjoy your meal
            </p>
          </div>
          <div className="mb-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8 text-green-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="p-6 space-y-3 bg-white border-t">
          <button
            onClick={handleViewStatusOrder}
            className="w-full bg-primary-500 text-white py-4 rounded-lg font-semibold hover:bg-primary-600 transition-colors text-lg"
          >
            View Status Order
          </button>

          <button
            onClick={handleBackToHome}
            className="w-full bg-secondary-200 text-secondary-700 py-3 rounded-lg font-medium hover:bg-secondary-300 transition-colors"
          >
            Back to Menu
          </button>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-20 left-8 opacity-10">
          <div className="w-32 h-32 border-2 border-primary-200 rounded-full"></div>
        </div>
        <div className="absolute bottom-40 right-8 opacity-10">
          <div className="w-24 h-24 border-2 border-secondary-200 rounded-full"></div>
        </div>
        <div className="absolute top-1/3 right-12 opacity-10">
          <div className="w-16 h-16 border-2 border-primary-200 rounded-full"></div>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
