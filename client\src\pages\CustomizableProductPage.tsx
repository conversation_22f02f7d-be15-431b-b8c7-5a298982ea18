import React, { useState, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAppDispatch } from "../hooks/redux";
import { addToCart } from "../store/slices/cartSlice";
import { getProductById } from "../data/products";

// Icons
const ArrowLeftIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
  </svg>
);

const CartIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9" />
  </svg>
);

const PlusIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
  </svg>
);

const MinusIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
  </svg>
);

const ChevronDownIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
  </svg>
);

// Customization options data
const customizationOptions = {
  "Petit dej Express": {
    required: ["Café", "Jus"],
    categories: {
      "Café": [
        { name: "Café crème", price: 0 },
        { name: "Express", price: 2 },
        { name: "Americain", price: 3 },
        { name: "Capuccin", price: 4 }
      ],
      "Jus": [
        { name: "Orange", price: 5 },
        { name: "Pomme", price: 4 },
        { name: "Ananas", price: 6 },
        { name: "Mixte", price: 7 }
      ],
      "Omelette nature": [
        { name: "Simple", price: 0 },
        { name: "Aux herbes", price: 2 },
        { name: "Au fromage", price: 3 }
      ],
      "Oeuf dur": [
        { name: "1 oeuf", price: 0 },
        { name: "2 oeufs", price: 2 }
      ]
    }
  },
  "healthy brunch": {
    required: ["Café", "Jus"],
    categories: {
      "Café": [
        { name: "Café crème", price: 0 },
        { name: "Express", price: 2 },
        { name: "Americain", price: 3 },
        { name: "Capuccin", price: 4 }
      ],
      "Jus": [
        { name: "Orange", price: 5 },
        { name: "Pomme", price: 4 },
        { name: "Ananas", price: 6 },
        { name: "Mixte", price: 7 }
      ],
      "croissant": [
        { name: "Nature", price: 0 },
        { name: "Chocolat", price: 2 },
        { name: "Amande", price: 3 }
      ],
      "Eau": [
        { name: "Plate", price: 0 },
        { name: "Gazeuse", price: 1 }
      ]
    }
  }
};

interface CustomizationItem {
  name: string;
  price: number;
}

interface CategorySelection {
  [category: string]: {
    [itemName: string]: number;
  };
}

const CustomizableProductPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const product = id ? getProductById(parseInt(id)) : null;
  const [selections, setSelections] = useState<CategorySelection>({});
  const [openDropdowns, setOpenDropdowns] = useState<{ [key: string]: boolean }>({});

  if (!product) {
    return (
      <div className="bg-secondary-50 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-secondary-800 mb-4">Product Not Found</h2>
          <button
            onClick={() => navigate("/")}
            className="bg-primary-500 text-white px-6 py-2 rounded-lg hover:bg-primary-600 transition-colors"
          >
            Go Back Home
          </button>
        </div>
      </div>
    );
  }

  const productCustomization = customizationOptions[product.name as keyof typeof customizationOptions];
  
  if (!productCustomization) {
    // Redirect to regular product detail if no customization available
    navigate(`/product/${id}`);
    return null;
  }

  const toggleDropdown = (category: string) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  const updateSelection = (category: string, itemName: string, quantity: number) => {
    setSelections(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [itemName]: Math.max(0, quantity)
      }
    }));
  };

  const getItemQuantity = (category: string, itemName: string): number => {
    return selections[category]?.[itemName] || 0;
  };

  // Calculate total price
  const totalPrice = useMemo(() => {
    let total = product.price;
    
    Object.entries(selections).forEach(([category, items]) => {
      Object.entries(items).forEach(([itemName, quantity]) => {
        const item = productCustomization.categories[category]?.find(i => i.name === itemName);
        if (item && quantity > 0) {
          total += item.price * quantity;
        }
      });
    });
    
    return total;
  }, [selections, product.price, productCustomization]);

  // Check if all required categories have at least one selection
  const canAddToCart = useMemo(() => {
    return productCustomization.required.every(requiredCategory => {
      const categorySelections = selections[requiredCategory];
      if (!categorySelections) return false;
      return Object.values(categorySelections).some(quantity => quantity > 0);
    });
  }, [selections, productCustomization.required]);

  const handleAddToCart = () => {
    if (!canAddToCart) return;
    
    // Create customized product with selections
    const customizedProduct = {
      ...product,
      customizations: selections,
      price: totalPrice
    };
    
    dispatch(addToCart({ product: customizedProduct, quantity: 1 }));
    navigate("/");
  };

  return (
    <div className="bg-secondary-50 min-h-screen font-sans">
      <div className="max-w-md mx-auto bg-white shadow-lg">
        {/* Header */}
        <div className="flex justify-between items-center p-4 bg-white">
          <button
            onClick={() => navigate("/")}
            className="p-2 rounded-full hover:bg-secondary-100 transition-colors"
          >
            <ArrowLeftIcon />
          </button>
          <h1 className="text-lg font-semibold text-secondary-800">{product.name}</h1>
          <div className="p-2">
            <CartIcon />
          </div>
        </div>

        {/* Product Image */}
        <div className="px-4">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-48 rounded-xl object-cover"
          />
        </div>

        {/* Customization Categories */}
        <div className="p-4 space-y-4">
          {Object.entries(productCustomization.categories).map(([category, items]) => (
            <div key={category} className="bg-white border border-secondary-200 rounded-lg">
              {/* Category Header */}
              <button
                onClick={() => toggleDropdown(category)}
                className="w-full flex items-center justify-between p-4 text-left"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center">
                    <span className="text-sm">☕</span>
                  </div>
                  <div>
                    <span className="font-medium text-secondary-800">{category}</span>
                    {productCustomization.required.includes(category) && (
                      <span className="text-red-500 text-sm ml-1">*</span>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-secondary-500">choisir</span>
                  <ChevronDownIcon />
                </div>
              </button>

              {/* Category Items */}
              {openDropdowns[category] && (
                <div className="border-t border-secondary-200">
                  {items.map((item: CustomizationItem) => (
                    <div key={item.name} className="flex items-center justify-between p-4 border-b border-secondary-100 last:border-b-0">
                      <div className="flex-1">
                        <span className="text-secondary-800">{item.name}</span>
                        {item.price > 0 && (
                          <span className="text-sm text-secondary-500 ml-2">+{item.price} DT</span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => updateSelection(category, item.name, getItemQuantity(category, item.name) - 1)}
                          className="w-8 h-8 rounded-full border border-secondary-300 flex items-center justify-center hover:bg-secondary-50"
                          disabled={getItemQuantity(category, item.name) === 0}
                        >
                          <MinusIcon />
                        </button>
                        <span className="w-8 text-center font-medium">
                          {getItemQuantity(category, item.name)}
                        </span>
                        <button
                          onClick={() => updateSelection(category, item.name, getItemQuantity(category, item.name) + 1)}
                          className="w-8 h-8 rounded-full border border-secondary-300 flex items-center justify-center hover:bg-secondary-50"
                        >
                          <PlusIcon />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Add to Cart Button */}
        <div className="p-4 border-t border-secondary-200">
          <button
            onClick={handleAddToCart}
            disabled={!canAddToCart}
            className={`w-full py-4 rounded-xl font-semibold flex items-center justify-center space-x-2 transition-colors ${
              canAddToCart
                ? "bg-primary-500 text-white hover:bg-primary-600"
                : "bg-secondary-300 text-secondary-500 cursor-not-allowed"
            }`}
          >
            <span>{totalPrice} DT - Add to cart</span>
            <CartIcon />
          </button>
          {!canAddToCart && (
            <p className="text-sm text-red-500 text-center mt-2">
              Please select at least one item from: {productCustomization.required.join(", ")}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomizableProductPage;
