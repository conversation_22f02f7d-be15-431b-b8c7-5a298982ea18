import { Request, Response, NextFunction } from "express";
import { BaseController } from "../../base/BaseController";
import { OrderService } from "./order.service";
import { CreateOrderDto, UpdateOrderStatusDto, OrderQueryDto, AssignWaiterDto } from "./order.dto";
import { ApiError } from "../../utils/ApiError";
import logger from "../../config/logger";

export class OrderController extends BaseController {
  protected controllerName = "OrderController";
  private orderService: OrderService;

  constructor() {
    super();
    this.orderService = new OrderService();
  }

  /**
   * @swagger
   * /api/orders:
   *   post:
   *     summary: Create a new order
   *     tags: [Orders]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateOrderDto'
   *     responses:
   *       201:
   *         description: Order created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Order'
   *       400:
   *         description: Bad request
   *       401:
   *         description: Unauthorized
   */
  createOrder = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const orderData: CreateOrderDto = req.body;
      const customerId = req.user!.id;

      const order = await this.orderService.createOrder(orderData, customerId);

      this.sendResponse(res, 201, order, "Order created successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/orders:
   *   get:
   *     summary: Get orders with filtering and pagination
   *     tags: [Orders]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *         description: Items per page
   *       - in: query
   *         name: status
   *         schema:
   *           type: string
   *           enum: [PENDING, CONFIRMED, PREPARING, READY, COMPLETED, CANCELLED]
   *         description: Filter by status
   *       - in: query
   *         name: type
   *         schema:
   *           type: string
   *           enum: [DINE_IN, TAKEAWAY, DELIVERY]
   *         description: Filter by type
   *       - in: query
   *         name: customerId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by customer ID
   *       - in: query
   *         name: tableId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Filter by table ID
   *       - in: query
   *         name: date
   *         schema:
   *           type: string
   *           format: date
   *         description: Filter by date (YYYY-MM-DD)
   *     responses:
   *       200:
   *         description: Orders retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 data:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Order'
   *                 meta:
   *                   $ref: '#/components/schemas/PaginationMeta'
   */
  getOrders = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const query: OrderQueryDto = req.query as any;
      const user = req.user!;
      
      let restaurantId: string;
      let customerId: string | undefined;

      // Determine access based on user role
      if (user.role === 'CLIENT') {
        // Clients can only see their own orders
        customerId = user.id;
        restaurantId = req.query.restaurantId as string;
        
        if (!restaurantId) {
          throw ApiError.badRequest("Restaurant ID is required");
        }
      } else {
        // Staff can see restaurant orders
        restaurantId = user.restaurantId || req.query.restaurantId as string;
        
        if (!restaurantId) {
          throw ApiError.badRequest("Restaurant ID is required");
        }
      }

      const result = await this.orderService.getOrders(restaurantId, query, customerId);

      this.sendResponse(res, 200, result, "Orders retrieved successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/orders/{id}:
   *   get:
   *     summary: Get order by ID
   *     tags: [Orders]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Order ID
   *     responses:
   *       200:
   *         description: Order retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Order'
   *       404:
   *         description: Order not found
   */
  getOrderById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const user = req.user!;
      
      let restaurantId: string | undefined;
      let customerId: string | undefined;

      // Determine access based on user role
      if (user.role === 'CLIENT') {
        customerId = user.id;
      } else {
        restaurantId = user.restaurantId;
      }

      const order = await this.orderService.getOrderById(id, restaurantId, customerId);

      this.sendResponse(res, 200, order, "Order retrieved successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/orders/{id}/status:
   *   patch:
   *     summary: Update order status
   *     tags: [Orders]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Order ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateOrderStatusDto'
   *     responses:
   *       200:
   *         description: Order status updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Order'
   *       400:
   *         description: Bad request
   *       401:
   *         description: Unauthorized
   *       403:
   *         description: Forbidden
   *       404:
   *         description: Order not found
   */
  updateOrderStatus = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const statusData: UpdateOrderStatusDto = req.body;
      const restaurantId = req.user?.restaurantId;

      const order = await this.orderService.updateOrderStatus(id, statusData, restaurantId);

      this.sendResponse(res, 200, order, "Order status updated successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/orders/{id}/assign-waiter:
   *   patch:
   *     summary: Assign waiter to order
   *     tags: [Orders]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Order ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/AssignWaiterDto'
   *     responses:
   *       200:
   *         description: Waiter assigned successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Order'
   */
  assignWaiter = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const { waiterId }: AssignWaiterDto = req.body;
      const restaurantId = req.user?.restaurantId;

      const order = await this.orderService.assignWaiter(id, waiterId, restaurantId);

      this.sendResponse(res, 200, order, "Waiter assigned successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/orders/{id}/cancel:
   *   patch:
   *     summary: Cancel order
   *     tags: [Orders]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Order ID
   *     requestBody:
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               reason:
   *                 type: string
   *                 description: Cancellation reason
   *     responses:
   *       200:
   *         description: Order cancelled successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Order'
   */
  cancelOrder = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const user = req.user!;
      
      let restaurantId: string | undefined;
      let customerId: string | undefined;

      // Determine access based on user role
      if (user.role === 'CLIENT') {
        customerId = user.id;
      } else {
        restaurantId = user.restaurantId;
      }

      const order = await this.orderService.cancelOrder(id, reason, restaurantId, customerId);

      this.sendResponse(res, 200, order, "Order cancelled successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/orders/stats:
   *   get:
   *     summary: Get order statistics
   *     tags: [Orders]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: fromDate
   *         schema:
   *           type: string
   *           format: date
   *         description: From date (YYYY-MM-DD)
   *       - in: query
   *         name: toDate
   *         schema:
   *           type: string
   *           format: date
   *         description: To date (YYYY-MM-DD)
   *     responses:
   *       200:
   *         description: Order statistics retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/OrderStatsDto'
   */
  getOrderStats = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { fromDate, toDate } = req.query;
      const restaurantId = req.user?.restaurantId || req.query.restaurantId as string;

      if (!restaurantId) {
        throw ApiError.badRequest("Restaurant ID is required");
      }

      const stats = await this.orderService.getOrderStats(
        restaurantId,
        fromDate ? new Date(fromDate as string) : undefined,
        toDate ? new Date(toDate as string) : undefined
      );

      this.sendResponse(res, 200, stats, "Order statistics retrieved successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/orders/my-orders:
   *   get:
   *     summary: Get current user's orders
   *     tags: [Orders]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 50
   *         description: Maximum number of orders to return
   *     responses:
   *       200:
   *         description: Customer orders retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/Order'
   */
  getMyOrders = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const customerId = req.user!.id;
      const limit = parseInt(req.query.limit as string) || 20;

      const orders = await this.orderService.getCustomerOrders(customerId, limit);

      this.sendResponse(res, 200, orders, "Customer orders retrieved successfully");
    } catch (error) {
      next(error);
    }
  };
}
