import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  OneToMany,
  ManyToOne,
  JoinColumn,
  DeleteDateColumn,
} from "typeorm";
import {
  IsEmail,
  IsNotEmpty,
  MinLength,
  IsOptional,
  IsUUID,
  IsString,
  IsPhoneNumber,
} from "class-validator";
import { Session } from "../auth/session.entity";
import { UserRole } from "../../types/roles.enum";
import { Restaurant } from "../restaurant/restaurant.model";
import { Order } from "../order/order.model";

@Entity("users")
@Index(["email"], { unique: true })
export class User {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255, unique: true })
  @IsEmail({}, { message: "Please provide a valid email address" })
  @IsNotEmpty({ message: "Email is required" })
  email: string;

  @Column({ type: "varchar", length: 255 })
  @IsNotEmpty({ message: "Password is required" })
  @MinLength(6, { message: "Password must be at least 6 characters long" })
  password: string;

  @Column({ type: "varchar", length: 100 })
  @IsNotEmpty({ message: "First name is required" })
  firstName: string;

  @Column({ type: "varchar", length: 100 })
  @IsNotEmpty({ message: "Last name is required" })
  lastName: string;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @Column({
    type: "enum",
    enum: UserRole,
    default: UserRole.CLIENT,
  })
  role: UserRole;

  @Column({ type: "varchar", length: 20, nullable: true })
  @IsOptional()
  @IsPhoneNumber(null, { message: "Please provide a valid phone number" })
  phone?: string;

  @Column({ type: "varchar", length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: "Avatar URL must be a string" })
  avatarUrl?: string;

  // Restaurant relationship for staff members
  @Column({ type: "uuid", nullable: true })
  @IsOptional()
  @IsUUID(4, { message: "Restaurant ID must be a valid UUID" })
  restaurantId?: string;

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.staff, {
    nullable: true,
  })
  @JoinColumn({ name: "restaurantId" })
  restaurant?: Restaurant;

  @CreateDateColumn({ type: "timestamp" })
  createdAt: Date;

  @UpdateDateColumn({ type: "timestamp" })
  updatedAt: Date;

  // Relationships
  @OneToMany(() => Session, (session) => session.user, {
    cascade: true,
    onDelete: "CASCADE",
  })
  sessions: Session[];

  // Restaurants owned by this user (for restaurant owners)
  @OneToMany(() => Restaurant, (restaurant) => restaurant.owner)
  ownedRestaurants: Restaurant[];

  // Orders as customer
  @OneToMany(() => Order, (order) => order.customer)
  customerOrders: Order[];

  // Orders as waiter
  @OneToMany(() => Order, (order) => order.waiter)
  waiterOrders: Order[];

  @DeleteDateColumn()
  deletedAt?: Date;

  // Virtual properties
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  get isRestaurantOwner(): boolean {
    return this.role === UserRole.RESTAURANT_OWNER;
  }

  get isWaiter(): boolean {
    return this.role === UserRole.WAITER;
  }

  get isAdmin(): boolean {
    return this.role === UserRole.ADMIN;
  }

  get isClient(): boolean {
    return this.role === UserRole.CLIENT;
  }

  get isStaff(): boolean {
    return [
      UserRole.RESTAURANT_OWNER,
      UserRole.WAITER,
      UserRole.ADMIN,
    ].includes(this.role);
  }

  // Method to get user data without password
  toJSON() {
    const { password, ...userWithoutPassword } = this;
    return userWithoutPassword;
  }
}
