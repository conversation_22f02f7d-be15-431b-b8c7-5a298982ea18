import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from "typeorm";
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsNumber,
  Min,
  Max,
  IsArray,
  IsBoolean,
} from "class-validator";
import { Restaurant } from "../restaurant/restaurant.model";
import { Category } from "../category/category.model";
import { OrderItem } from "../order/order-item.model";

export interface NutritionInfo {
  calories: number;
  protein: string;
  carbs: string;
  fat: string;
}

export interface CustomizationOption {
  name: string;
  price: number;
}

export interface CustomizationCategory {
  name: string;
  required: boolean;
  options: CustomizationOption[];
}

@Entity("products")
@Index(["name", "restaurantId"])
export class Product {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255 })
  @IsNotEmpty({ message: "Product name is required" })
  @IsString({ message: "Product name must be a string" })
  name: string;

  @Column({ type: "text", nullable: true })
  @IsOptional()
  @IsString({ message: "Description must be a string" })
  description?: string;

  @Column({ type: "decimal", precision: 10, scale: 2 })
  @IsNotEmpty({ message: "Price is required" })
  @IsNumber({}, { message: "Price must be a number" })
  @Min(0, { message: "Price must be non-negative" })
  price: number;

  @Column({ type: "varchar", length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: "Image URL must be a string" })
  imageUrl?: string;

  @Column({ type: "decimal", precision: 3, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber({}, { message: "Rating must be a number" })
  @Min(0, { message: "Rating must be between 0 and 5" })
  @Max(5, { message: "Rating must be between 0 and 5" })
  rating?: number;

  @Column({ type: "int", default: 0 })
  @IsOptional()
  @IsNumber({}, { message: "Reviews count must be a number" })
  @Min(0, { message: "Reviews count must be non-negative" })
  reviewsCount: number;

  @Column({ type: "json", nullable: true })
  ingredients?: string[];

  @Column({ type: "json", nullable: true })
  nutritionInfo?: NutritionInfo;

  @Column({ type: "boolean", default: true })
  @IsOptional()
  @IsBoolean({ message: "isAvailable must be a boolean" })
  isAvailable: boolean;

  @Column({ type: "boolean", default: false })
  @IsOptional()
  @IsBoolean({ message: "isFeatured must be a boolean" })
  isFeatured: boolean;

  @Column({ type: "boolean", default: false })
  @IsOptional()
  @IsBoolean({ message: "isCustomizable must be a boolean" })
  isCustomizable: boolean;

  @Column({ type: "json", nullable: true })
  customizationCategories?: CustomizationCategory[];

  @Column({ type: "int", default: 0 })
  @IsOptional()
  @IsNumber({}, { message: "Sort order must be a number" })
  @Min(0, { message: "Sort order must be non-negative" })
  sortOrder: number;

  @Column({ type: "json", nullable: true })
  tags?: string[];

  @Column({ type: "int", nullable: true })
  @IsOptional()
  @IsNumber({}, { message: "Preparation time must be a number" })
  @Min(0, { message: "Preparation time must be non-negative" })
  preparationTimeMinutes?: number;

  // Relationships
  @Column({ type: "uuid" })
  restaurantId: string;

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.products)
  @JoinColumn({ name: "restaurantId" })
  restaurant: Restaurant;

  @Column({ type: "uuid" })
  categoryId: string;

  @ManyToOne(() => Category, (category) => category.products)
  @JoinColumn({ name: "categoryId" })
  category: Category;

  @OneToMany(() => OrderItem, (orderItem) => orderItem.product)
  orderItems: OrderItem[];

  // Timestamps
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  // Virtual properties
  get averageRating(): number {
    return this.rating || 0;
  }

  get totalOrders(): number {
    return this.orderItems?.length || 0;
  }

  get isPopular(): boolean {
    return this.reviewsCount > 50 && this.rating && this.rating >= 4.0;
  }
}
