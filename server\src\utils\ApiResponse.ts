/**
 * Represents a standardized structure for API responses.
 * @template T The type of the data payload included in the response.
 */
export class ApiResponse<T> {
  /**
   * Creates an instance of ApiResponse.
   * @param {boolean} success - Indicates if the request was successful.
   * @param {T | null} data - The data payload of the response. Should be null for error responses.
   * @param {string} [message] - An optional descriptive message about the outcome.
   * @param {any} [errors] - Optional error details, typically used only when the request is not successful.
   */
  constructor(
    public success: boolean,
    public data: T | null,
    public message?: string,
    public errors?: any
  ) {}
}
