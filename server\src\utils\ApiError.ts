/**
 * Custom error class for API errors
 */
export class ApiError extends <PERSON>rror {
  public statusCode: number;
  public isOperational: boolean;
  public errors?: any[];
  public code?: string;

  constructor(
    statusCode: number,
    message: string,
    errors?: any[],
    isOperational = true,
    stack = "",
    code?: string
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.errors = errors;
    this.code = code;

    if (stack) {
      this.stack = stack;
    } else {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Create a validation error
   */
  static validation(message: string, errors?: any[]): ApiError {
    return new ApiError(400, message, errors, true, "", "VALIDATION_ERROR");
  }

  /**
   * Create an unauthorized error
   */
  static unauthorized(message = "Authentication required"): ApiError {
    return new ApiError(401, message, undefined, true, "", "UNAUTHORIZED");
  }

  /**
   * Create a forbidden error
   */
  static forbidden(message = "Access denied"): ApiError {
    return new ApiError(403, message, undefined, true, "", "FORBIDDEN");
  }

  /**
   * Create a not found error
   */
  static notFound(message = "Resource not found"): ApiError {
    return new ApiError(404, message, undefined, true, "", "NOT_FOUND");
  }

  /**
   * Create a conflict error
   */
  static conflict(message: string): ApiError {
    return new ApiError(409, message, undefined, true, "", "CONFLICT");
  }

  /**
   * Create an internal server error
   */
  static internal(message = "Internal server error"): ApiError {
    return new ApiError(
      500,
      message,
      undefined,
      false,
      "",
      "INTERNAL_SERVER_ERROR"
    );
  }
}
