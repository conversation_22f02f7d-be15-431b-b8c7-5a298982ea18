import { Repository } from "typeorm";
import { dbConnection } from "../../database/connection";
import { User } from "../user/user.model";
import { Session } from "./session.entity";

export class AuthRepository {
  private userRepository: Repository<User>;
  private sessionRepository: Repository<Session>;

  constructor() {
    this.userRepository = dbConnection.getRepository(User);
    this.sessionRepository = dbConnection.getRepository(Session);
  }

  // User operations
  async createUser(userData: Partial<User>): Promise<User> {
    const user = this.userRepository.create(userData);
    return await this.userRepository.save(user);
  }

  async findUserByEmail(email: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { email },
    });
  }

  async findUserById(id: string): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { id },
    });
  }

  async updateUser(id: string, userData: Partial<User>): Promise<User | null> {
    await this.userRepository.update(id, userData);
    return await this.findUserById(id);
  }

  // Session operations
  async createSession(sessionData: Partial<Session>): Promise<Session> {
    const session = this.sessionRepository.create(sessionData);
    return await this.sessionRepository.save(session);
  }

  async findSessionByToken(token: string): Promise<Session | null> {
    return await this.sessionRepository.findOne({
      where: { token, isActive: true },
      relations: ["user"],
    });
  }

  async findSessionsByUserId(userId: string): Promise<Session[]> {
    return await this.sessionRepository.find({
      where: { userId, isActive: true },
      order: { createdAt: "DESC" },
    });
  }

  async deactivateSession(sessionId: string): Promise<void> {
    await this.sessionRepository.update(sessionId, { isActive: false });
  }

  async deactivateAllUserSessions(userId: string): Promise<void> {
    await this.sessionRepository.update(
      { userId, isActive: true },
      { isActive: false }
    );
  }

  async cleanupExpiredSessions(): Promise<void> {
    await this.sessionRepository
      .createQueryBuilder()
      .delete()
      .from(Session)
      .where("expiresAt < :now", { now: new Date() })
      .execute();
  }

  async deleteSession(sessionId: string): Promise<void> {
    await this.sessionRepository.delete(sessionId);
  }
}
