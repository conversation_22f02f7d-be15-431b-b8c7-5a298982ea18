// Product types
export interface Product {
  id: number;
  name: string;
  category: string;
  price: number;
  image: string;
  description?: string;
  rating?: number;
  reviews?: number;
  ingredients?: string[];
  nutritionInfo?: {
    calories: number;
    protein: string;
    carbs: string;
    fat: string;
  };
}

// Cart types
export interface CartItem {
  id: number;
  product: Product;
  quantity: number;
  totalPrice: number;
}

export interface CartState {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  isOpen: boolean;
}

// Category types
export type Category =
  | "All"
  | "Brunch"
  | "Café"
  | "Cookies"
  | "Fondant"
  | "Desserts";
