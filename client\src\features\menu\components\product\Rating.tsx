import { StarIcon } from "../../../../assets/icons/component/StarIcon";
import { Product } from "../../types/menuTypes";

export default function Rating({ product }: { product: Product }) {
  const renderStars = (rating?: number) => {
    if (!rating) return null;

    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      stars.push(
        <StarIcon
          key={i}
          filled={i < fullStars || (i === fullStars && hasHalfStar)}
        />
      );
    }
    return stars;
  };

  return (
    <div className="flex items-center mb-4">
      <div className="flex items-center mr-2">
        {renderStars(product.rating)}
      </div>
      <span className="text-secondary-600 text-sm">
        {product.rating} ({product.reviews} reviews)
      </span>
    </div>
  );
}
