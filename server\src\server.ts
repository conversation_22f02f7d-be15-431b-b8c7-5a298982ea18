import "reflect-metadata";
import http from "http";
import { Application } from "express";
import { App } from "./app";
import { dbConnection } from "./database/connection";
import logger from "./config/logger";

class Server {
  private app: Application;

  constructor(app: Application) {
    this.app = app;
  }

  public start(): void {
    const port: number = Number(process.env.PORT) || 5000;
    const server: http.Server = http.createServer(this.app);
    server.listen(port, () => {
      logger.info(`Server is listening on port ${port}`);
    });
  }
}

// Initialize the database connection
dbConnection
  .initialize()
  .then(() => {
    logger.info(`Database connected successfully`);
    const app = new App();
    const server = new Server(app.express);
    server.start();
  })
  .catch((error) => {
    logger.error("Error connecting to the database", error);
  });
