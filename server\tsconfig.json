{"compilerOptions": {"target": "es2022", "module": "commonjs", "esModuleInterop": true, "outDir": "./dist", "sourceMap": true, "strict": true, "strictPropertyInitialization": false, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@config/*": ["./config/*"], "@features/*": ["./features/*"]}, "skipLibCheck": true, "forceConsistentCasingInFileNames": false, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*.ts", "src/**/*.handlebars", "src/types/express.d.ts", "@types/pmx.d.ts"], "exclude": ["node_modules", "**/*.spec.ts", "__test__"]}