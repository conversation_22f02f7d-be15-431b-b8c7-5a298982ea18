import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector, useAppDispatch } from "../hooks/redux";
import {
  selectSelectedCategory,
  selectSearchQuery,
  setSelectedCategory,
} from "../store/slices/filterSlice";
import { selectCartTotalItems, toggleCart } from "../store/slices/cartSlice";
import { getProductsByCategory } from "../data/products";
import Cart from "../components/Cart";
import { UserIcon } from "../assets/icons/component/UserIcon";
import { categories } from "../features/menu/data/menuData";

// Category images mapping
const categoryImages: Record<string, string> = {
  All: "https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=400&h=300&fit=crop&crop=center",
  Brunch:
    "https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&crop=center",
  Café: "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=400&h=300&fit=crop&crop=center",
  Hlow: "https://images.unsplash.com/photo-1551218808-94e220e084d2?w=400&h=300&fit=crop&crop=center",
  Gateaux:
    "https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop&crop=center",
};

// Header Component for MenuHomePage
const MenuHeader: React.FC = () => {
  const dispatch = useAppDispatch();
  const cartItemCount = useAppSelector(selectCartTotalItems);
  const [activeTab, setActiveTab] = useState<"menu" | "commande">("menu");

  return (
    <div className="bg-white">
      {/* Logo Section */}
      <div className="text-center py-6">
        <div className="relative inline-block">
          <div
            className="text-3xl font-bold text-primary-500 mb-1"
            style={{ fontFamily: "serif" }}
          >
            Logo
          </div>
          <div className="text-sm text-secondary-400 italic tracking-wider">
            Madame Crok
          </div>
          {/* Decorative leaf element */}
          <div className="absolute -top-1 -right-6">
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-secondary-400"
            >
              <path
                d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21Z"
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="px-4 pb-4">
        <div className="flex bg-secondary-100 rounded-full p-1">
          <button
            onClick={() => setActiveTab("menu")}
            className={`flex-1 flex items-center justify-center py-3 px-4 rounded-full font-medium transition-colors ${
              activeTab === "menu"
                ? "bg-white text-secondary-800 shadow-sm"
                : "text-secondary-600"
            }`}
          >
            <span className="mr-2">📋</span>
            Menu
          </button>
          <button
            onClick={() => {
              setActiveTab("commande");
              dispatch(toggleCart());
            }}
            className={`flex-1 flex items-center justify-center py-3 px-4 rounded-full font-medium transition-colors relative ${
              activeTab === "commande"
                ? "bg-white text-secondary-800 shadow-sm"
                : "text-secondary-600"
            }`}
          >
            <span className="mr-2">🛒</span>
            Commande
            {cartItemCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
                {cartItemCount}
              </span>
            )}
          </button>
        </div>
      </div>

      {/* User Icon - positioned in top right */}
      <div className="absolute top-4 right-4">
        <UserIcon />
      </div>
    </div>
  );
};

// Visual Categories Component
const VisualCategories: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const visualCategories = categories.filter((cat) => cat !== "All");

  // Map categories to their customizable product IDs (for Brunch only)
  const categoryToProductId: Record<string, number> = {
    Brunch: 13, // healthy brunch - more customizable option
  };

  // Categories that should show category detail page (list of products)
  const categoryDetailCategories = ["Café"];

  return (
    <div className="p-4">
      {/* Large visual category cards */}
      <div className="space-y-4">
        {visualCategories.map((category: any) => (
          <div
            key={`card-${category}`}
            className="relative rounded-2xl overflow-hidden shadow-lg cursor-pointer transform transition-transform hover:scale-[1.02]"
            onClick={() => dispatch(setSelectedCategory(category))}
          >
            <div className="relative h-48">
              <img
                src={categoryImages[category] || categoryImages["All"]}
                alt={category}
                className="w-full h-full object-cover"
              />
              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>

              {/* Category title */}
              <div className="absolute bottom-4 left-4">
                <h3 className="text-white text-2xl font-bold mb-1">
                  {category}
                </h3>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    const productId = categoryToProductId[category];

                    if (productId) {
                      // Navigate to customizable product page (for Brunch)
                      navigate(`/customize/${productId}`);
                    } else if (categoryDetailCategories.includes(category)) {
                      // Navigate to category detail page (for Coffee and other categories)
                      navigate(`/category/${category}`);
                    } else {
                      // Default behavior - filter by category
                      dispatch(setSelectedCategory(category));
                    }
                  }}
                  className="text-white text-sm underline hover:no-underline transition-all"
                >
                  voir plus
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const MenuHomePage: React.FC = () => {
  const selectedCategory = useAppSelector(selectSelectedCategory);
  const searchQuery = useAppSelector(selectSearchQuery);

  // Filter products based on category and search query
  const filteredProducts = useMemo(() => {
    let filtered = getProductsByCategory(selectedCategory);

    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [selectedCategory, searchQuery]);

  return (
    <div className="bg-secondary-50 min-h-screen font-sans">
      <div className="max-w-md mx-auto bg-white shadow-lg relative">
        <MenuHeader />
        <VisualCategories />
        <Cart />
      </div>
    </div>
  );
};

export default MenuHomePage;
