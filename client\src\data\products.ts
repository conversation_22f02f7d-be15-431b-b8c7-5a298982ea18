import { Product } from "../features/menu/types/menuTypes";

export const products: Product[] = [
  // Cookies Category
  {
    id: 1,
    name: "Cookies with bite of Choco",
    category: "Cookies",
    price: 8,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Cookies",
    description:
      "Delicious chocolate chip cookies with a perfect bite of chocolate in every piece. Made with premium ingredients and baked fresh daily.",
    rating: 4.5,
    reviews: 128,
    ingredients: [
      "Flour",
      "Chocolate chips",
      "Butter",
      "Sugar",
      "Eggs",
      "Vanilla",
    ],
    nutritionInfo: {
      calories: 180,
      protein: "3g",
      carbs: "24g",
      fat: "8g",
    },
  },
  {
    id: 5,
    name: "Oatmeal Raisin Cookies",
    category: "Cookies",
    price: 7,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Oatmeal",
    description:
      "Classic oatmeal cookies with plump raisins and a hint of cinnamon.",
    rating: 4.2,
    reviews: 95,
    ingredients: [
      "Oats",
      "Raisins",
      "Flour",
      "Butter",
      "Brown sugar",
      "Cinnamon",
    ],
    nutritionInfo: {
      calories: 165,
      protein: "3g",
      carbs: "22g",
      fat: "7g",
    },
  },

  // Fondant Category
  {
    id: 2,
    name: "Fondant au chocolat",
    category: "Fondant",
    price: 15,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Fondant",
    description:
      "Rich and creamy chocolate fondant that melts in your mouth. A perfect dessert for chocolate lovers.",
    rating: 4.8,
    reviews: 89,
    ingredients: ["Dark chocolate", "Butter", "Eggs", "Sugar", "Flour"],
    nutritionInfo: {
      calories: 320,
      protein: "6g",
      carbs: "28g",
      fat: "22g",
    },
  },
  {
    id: 6,
    name: "Vanilla Fondant",
    category: "Fondant",
    price: 14,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Vanilla",
    description:
      "Smooth vanilla fondant with a delicate flavor and creamy texture.",
    rating: 4.6,
    reviews: 67,
    ingredients: ["Vanilla", "Butter", "Eggs", "Sugar", "Flour", "Cream"],
    nutritionInfo: {
      calories: 295,
      protein: "5g",
      carbs: "26g",
      fat: "20g",
    },
  },

  // Brunch Category
  {
    id: 3,
    name: "Petit dej Express",
    category: "Brunch",
    price: 25,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Brunch",
    description:
      "Quick and delicious breakfast option perfect for busy mornings. Includes fresh bread, jam, and coffee.",
    rating: 4.2,
    reviews: 156,
    ingredients: ["Fresh bread", "Homemade jam", "Butter", "Coffee"],
    nutritionInfo: {
      calories: 450,
      protein: "12g",
      carbs: "65g",
      fat: "18g",
    },
  },
  {
    id: 13,
    name: "healthy brunch",
    category: "Brunch",
    price: 49,
    image:
      "https://images.unsplash.com/photo-**********-94e220e084d2?w=400&h=300&fit=crop&crop=center",
    description:
      "A nutritious and delicious brunch option with fresh ingredients, perfect for health-conscious food lovers.",
    rating: 4.7,
    reviews: 89,
    ingredients: [
      "Granola bowl",
      "Fresh fruits",
      "Organic coffee",
      "Natural juice",
    ],
    nutritionInfo: {
      calories: 380,
      protein: "15g",
      carbs: "45g",
      fat: "12g",
    },
  },
  {
    id: 7,
    name: "Avocado Toast Deluxe",
    category: "Brunch",
    price: 18,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Avocado",
    description:
      "Fresh avocado on artisan bread with cherry tomatoes and a poached egg.",
    rating: 4.4,
    reviews: 134,
    ingredients: [
      "Avocado",
      "Artisan bread",
      "Cherry tomatoes",
      "Poached egg",
      "Olive oil",
    ],
    nutritionInfo: {
      calories: 385,
      protein: "15g",
      carbs: "32g",
      fat: "24g",
    },
  },
  {
    id: 8,
    name: "French Toast Stack",
    category: "Brunch",
    price: 22,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=French+Toast",
    description:
      "Three layers of fluffy French toast with maple syrup and fresh berries.",
    rating: 4.7,
    reviews: 198,
    ingredients: [
      "Brioche bread",
      "Eggs",
      "Milk",
      "Vanilla",
      "Maple syrup",
      "Berries",
    ],
    nutritionInfo: {
      calories: 520,
      protein: "18g",
      carbs: "68g",
      fat: "22g",
    },
  },

  // Café Category
  {
    id: 4,
    name: "Café Americano",
    category: "Café",
    price: 12,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Coffee",
    description:
      "Rich and bold American-style coffee to start your day right. Made from premium coffee beans.",
    rating: 4.6,
    reviews: 203,
    ingredients: ["Premium coffee beans", "Hot water"],
    nutritionInfo: {
      calories: 5,
      protein: "0g",
      carbs: "1g",
      fat: "0g",
    },
  },
  {
    id: 9,
    name: "Cappuccino Supreme",
    category: "Café",
    price: 15,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Cappuccino",
    description:
      "Perfect balance of espresso, steamed milk, and foam with a touch of cocoa.",
    rating: 4.5,
    reviews: 167,
    ingredients: ["Espresso", "Steamed milk", "Milk foam", "Cocoa powder"],
    nutritionInfo: {
      calories: 120,
      protein: "6g",
      carbs: "12g",
      fat: "6g",
    },
  },
  {
    id: 10,
    name: "Iced Caramel Latte",
    category: "Café",
    price: 16,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Iced+Latte",
    description:
      "Refreshing iced latte with house-made caramel syrup and whipped cream.",
    rating: 4.3,
    reviews: 142,
    ingredients: [
      "Espresso",
      "Cold milk",
      "Caramel syrup",
      "Ice",
      "Whipped cream",
    ],
    nutritionInfo: {
      calories: 280,
      protein: "8g",
      carbs: "35g",
      fat: "12g",
    },
  },
  {
    id: 14,
    name: "Espresso Double Shot",
    category: "Café",
    price: 10,
    image:
      "https://images.unsplash.com/photo-1510707577719-ae7c14805e3a?w=400&h=300&fit=crop&crop=center",
    description:
      "Strong and intense double shot espresso for the true coffee connoisseur.",
    rating: 4.8,
    reviews: 95,
    ingredients: ["Premium espresso beans"],
    nutritionInfo: {
      calories: 10,
      protein: "1g",
      carbs: "2g",
      fat: "0g",
    },
  },
  {
    id: 15,
    name: "Vanilla Latte",
    category: "Café",
    price: 14,
    image:
      "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400&h=300&fit=crop&crop=center",
    description:
      "Smooth espresso with steamed milk and a hint of vanilla sweetness.",
    rating: 4.4,
    reviews: 178,
    ingredients: ["Espresso", "Steamed milk", "Vanilla syrup"],
    nutritionInfo: {
      calories: 190,
      protein: "7g",
      carbs: "20g",
      fat: "8g",
    },
  },

  // Desserts Category
  {
    id: 11,
    name: "Tiramisu Classic",
    category: "Desserts",
    price: 18,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Tiramisu",
    description:
      "Traditional Italian tiramisu with layers of coffee-soaked ladyfingers and mascarpone.",
    rating: 4.9,
    reviews: 234,
    ingredients: [
      "Ladyfingers",
      "Mascarpone",
      "Coffee",
      "Cocoa",
      "Eggs",
      "Sugar",
    ],
    nutritionInfo: {
      calories: 420,
      protein: "8g",
      carbs: "45g",
      fat: "24g",
    },
  },
  {
    id: 12,
    name: "Chocolate Lava Cake",
    category: "Desserts",
    price: 16,
    image: "https://placehold.co/400x300/FFF4F4/C7A1A1?text=Lava+Cake",
    description:
      "Warm chocolate cake with a molten chocolate center, served with vanilla ice cream.",
    rating: 4.8,
    reviews: 189,
    ingredients: [
      "Dark chocolate",
      "Butter",
      "Eggs",
      "Sugar",
      "Flour",
      "Vanilla ice cream",
    ],
    nutritionInfo: {
      calories: 480,
      protein: "7g",
      carbs: "52g",
      fat: "28g",
    },
  },
];

// Categories for filtering
export const categories = [
  "All",
  "Brunch",
  "Café",
  "Cookies",
  "Fondant",
  "Desserts",
] as const;

export const bestOfTodayIds = [1, 2]; // IDs of featured products

export const getBestOfTodayProducts = (): Product[] => {
  return products.filter((product) => bestOfTodayIds.includes(product.id));
};

export const getProductsByCategory = (category: string): Product[] => {
  if (category === "All") return products;
  return products.filter((product) => product.category === category);
};

export const getProductById = (id: number): Product | undefined => {
  return products.find((product) => product.id === id);
};
