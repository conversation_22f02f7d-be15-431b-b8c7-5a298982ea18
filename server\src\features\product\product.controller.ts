import { Request, Response, NextFunction } from "express";
import { BaseController } from "../../base/BaseController";
import { ProductService } from "./product.service";
import {
  CreateProductDto,
  UpdateProductDto,
  ProductQueryDto,
} from "./product.dto";
import { ApiError } from "../../utils/ApiError";

export class ProductController extends BaseController {
  protected controllerName = "ProductController";
  private productService: ProductService;

  constructor() {
    super();
    this.productService = new ProductService();
  }

  /**
   * @swagger
   * /api/products:
   *   post:
   *     summary: Create a new product
   *     tags: [Products]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateProductDto'
   *     responses:
   *       201:
   *         description: Product created successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Product'
   *       400:
   *         description: Bad request
   *       401:
   *         description: Unauthorized
   *       403:
   *         description: Forbidden
   */
  createProduct = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const productData: CreateProductDto = req.body;

      // Ensure restaurant ID matches user's restaurant (for non-admin users)
      if (req.user?.role !== "admin" && req.user?.restaurantId) {
        productData.restaurantId = req.user.restaurantId;
      }

      const product = await this.productService.createProduct(productData);

      this.sendResponse(res, 201, product, "Product created successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/products:
   *   get:
   *     summary: Get products with filtering and pagination
   *     tags: [Products]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *         description: Page number
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *         description: Items per page
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term
   *       - in: query
   *         name: categoryId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Category ID
   *       - in: query
   *         name: isAvailable
   *         schema:
   *           type: boolean
   *         description: Filter by availability
   *       - in: query
   *         name: isFeatured
   *         schema:
   *           type: boolean
   *         description: Filter by featured products
   *       - in: query
   *         name: sortBy
   *         schema:
   *           type: string
   *         description: Sort by field
   *       - in: query
   *         name: sortOrder
   *         schema:
   *           type: string
   *           enum: [asc, desc]
   *         description: Sort order
   *     responses:
   *       200:
   *         description: Products retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 data:
   *                   type: array
   *                   items:
   *                     $ref: '#/components/schemas/Product'
   *                 meta:
   *                   $ref: '#/components/schemas/PaginationMeta'
   */
  getProducts = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const query: ProductQueryDto = req.query as any;

      // Get restaurant ID from user context or query
      const restaurantId =
        req.user?.restaurantId || (req.query.restaurantId as string);

      if (!restaurantId) {
        throw ApiError.badRequest("Restaurant ID is required");
      }

      const result = await this.productService.getProducts(restaurantId, query);

      this.sendResponse(res, 200, result, "Products retrieved successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/products/{id}:
   *   get:
   *     summary: Get product by ID
   *     tags: [Products]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Product ID
   *     responses:
   *       200:
   *         description: Product retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Product'
   *       404:
   *         description: Product not found
   */
  getProductById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const restaurantId = req.user?.restaurantId;

      const product = await this.productService.getProductById(
        id,
        restaurantId
      );

      this.sendResponse(res, 200, product, "Product retrieved successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/products/{id}:
   *   put:
   *     summary: Update product
   *     tags: [Products]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Product ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateProductDto'
   *     responses:
   *       200:
   *         description: Product updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Product'
   *       400:
   *         description: Bad request
   *       401:
   *         description: Unauthorized
   *       403:
   *         description: Forbidden
   *       404:
   *         description: Product not found
   */
  updateProduct = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const updateData: UpdateProductDto = req.body;
      const restaurantId = req.user?.restaurantId;

      const product = await this.productService.updateProduct(
        id,
        updateData,
        restaurantId
      );

      this.sendResponse(res, 200, product, "Product updated successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/products/{id}:
   *   delete:
   *     summary: Delete product
   *     tags: [Products]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Product ID
   *     responses:
   *       200:
   *         description: Product deleted successfully
   *       401:
   *         description: Unauthorized
   *       403:
   *         description: Forbidden
   *       404:
   *         description: Product not found
   */
  deleteProduct = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const restaurantId = req.user?.restaurantId;

      await this.productService.deleteProduct(id, restaurantId);

      this.sendResponse(res, 200, null, "Product deleted successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/products/category/{categoryId}:
   *   get:
   *     summary: Get products by category
   *     tags: [Products]
   *     parameters:
   *       - in: path
   *         name: categoryId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Category ID
   *     responses:
   *       200:
   *         description: Products retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/Product'
   */
  getProductsByCategory = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { categoryId } = req.params;
      const restaurantId =
        req.user?.restaurantId || (req.query.restaurantId as string);

      if (!restaurantId) {
        throw ApiError.badRequest("Restaurant ID is required");
      }

      const products = await this.productService.getProductsByCategory(
        restaurantId,
        categoryId
      );

      this.sendResponse(res, 200, products, "Products retrieved successfully");
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/products/featured:
   *   get:
   *     summary: Get featured products
   *     tags: [Products]
   *     parameters:
   *       - in: query
   *         name: restaurantId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Restaurant ID
   *     responses:
   *       200:
   *         description: Featured products retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/Product'
   */
  getFeaturedProducts = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const restaurantId =
        req.user?.restaurantId || (req.query.restaurantId as string);

      if (!restaurantId) {
        throw ApiError.badRequest("Restaurant ID is required");
      }

      const products = await this.productService.getFeaturedProducts(
        restaurantId
      );

      this.sendResponse(
        res,
        200,
        products,
        "Featured products retrieved successfully"
      );
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/products/search:
   *   get:
   *     summary: Search products
   *     tags: [Products]
   *     parameters:
   *       - in: query
   *         name: q
   *         required: true
   *         schema:
   *           type: string
   *         description: Search term
   *       - in: query
   *         name: restaurantId
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Restaurant ID
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 50
   *         description: Maximum number of results
   *     responses:
   *       200:
   *         description: Search results retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 $ref: '#/components/schemas/Product'
   */
  searchProducts = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { q: searchTerm, limit = 20 } = req.query;
      const restaurantId =
        req.user?.restaurantId || (req.query.restaurantId as string);

      if (!restaurantId) {
        throw ApiError.badRequest("Restaurant ID is required");
      }

      if (!searchTerm) {
        throw ApiError.badRequest("Search term is required");
      }

      const products = await this.productService.searchProducts(
        restaurantId,
        searchTerm as string,
        parseInt(limit as string)
      );

      this.sendResponse(
        res,
        200,
        products,
        "Search results retrieved successfully"
      );
    } catch (error) {
      next(error);
    }
  };

  /**
   * @swagger
   * /api/products/{id}/availability:
   *   patch:
   *     summary: Update product availability
   *     tags: [Products]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Product ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               isAvailable:
   *                 type: boolean
   *             required:
   *               - isAvailable
   *     responses:
   *       200:
   *         description: Product availability updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Product'
   */
  updateProductAvailability = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { id } = req.params;
      const { isAvailable } = req.body;
      const restaurantId = req.user?.restaurantId;

      if (typeof isAvailable !== "boolean") {
        throw ApiError.badRequest("isAvailable must be a boolean");
      }

      const product = await this.productService.updateProductAvailability(
        id,
        isAvailable,
        restaurantId
      );

      this.sendResponse(
        res,
        200,
        product,
        "Product availability updated successfully"
      );
    } catch (error) {
      next(error);
    }
  };
}
