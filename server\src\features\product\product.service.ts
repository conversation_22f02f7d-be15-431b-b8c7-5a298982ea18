import { BaseService } from "../../base/BaseService";
import { Product } from "./product.model";
import { ProductRepository } from "./product.repository";
import { CreateProductDto, UpdateProductDto, ProductQueryDto } from "./product.dto";
import { ApiError } from "../../utils/ApiError";
import { PaginatedResult } from "../../base/BaseModel";
import logger from "../../config/logger";

export class ProductService extends BaseService<Product> {
  private productRepository: ProductRepository;

  constructor() {
    super();
    this.productRepository = new ProductRepository();
  }

  /**
   * Create a new product
   */
  async createProduct(productData: CreateProductDto): Promise<Product> {
    try {
      logger.info("Creating new product", { productData });

      // Check if product with same name exists in the restaurant
      const existingProduct = await this.productRepository.findOne({
        where: {
          name: productData.name,
          restaurantId: productData.restaurantId,
          deletedAt: null,
        },
      });

      if (existingProduct) {
        throw ApiError.badRequest(
          "Product with this name already exists in the restaurant"
        );
      }

      // Create the product
      const product = this.productRepository.create(productData);
      const savedProduct = await this.productRepository.save(product);

      logger.info("Product created successfully", { productId: savedProduct.id });

      return await this.productRepository.findByIdWithRelations(savedProduct.id) as Product;
    } catch (error) {
      logger.error("Error creating product", { error, productData });
      throw error;
    }
  }

  /**
   * Get products with filtering and pagination
   */
  async getProducts(
    restaurantId: string,
    query: ProductQueryDto
  ): Promise<PaginatedResult<Product>> {
    try {
      logger.info("Fetching products", { restaurantId, query });

      const result = await this.productRepository.findProducts(restaurantId, query);

      logger.info("Products fetched successfully", {
        restaurantId,
        count: result.data.length,
        total: result.meta.total,
      });

      return result;
    } catch (error) {
      logger.error("Error fetching products", { error, restaurantId, query });
      throw error;
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(id: string, restaurantId?: string): Promise<Product> {
    try {
      logger.info("Fetching product by ID", { id, restaurantId });

      const product = await this.productRepository.findByIdWithRelations(id);

      if (!product) {
        throw ApiError.notFound("Product not found");
      }

      // Check restaurant access if provided
      if (restaurantId && product.restaurantId !== restaurantId) {
        throw ApiError.forbidden("Access denied to this product");
      }

      logger.info("Product fetched successfully", { productId: id });

      return product;
    } catch (error) {
      logger.error("Error fetching product", { error, id, restaurantId });
      throw error;
    }
  }

  /**
   * Update product
   */
  async updateProduct(
    id: string,
    updateData: UpdateProductDto,
    restaurantId?: string
  ): Promise<Product> {
    try {
      logger.info("Updating product", { id, updateData, restaurantId });

      const product = await this.getProductById(id, restaurantId);

      // Check if name is being changed and if it conflicts
      if (updateData.name && updateData.name !== product.name) {
        const existingProduct = await this.productRepository.findOne({
          where: {
            name: updateData.name,
            restaurantId: product.restaurantId,
            deletedAt: null,
          },
        });

        if (existingProduct && existingProduct.id !== id) {
          throw ApiError.badRequest(
            "Product with this name already exists in the restaurant"
          );
        }
      }

      // Update the product
      await this.productRepository.update(id, updateData);

      const updatedProduct = await this.productRepository.findByIdWithRelations(id) as Product;

      logger.info("Product updated successfully", { productId: id });

      return updatedProduct;
    } catch (error) {
      logger.error("Error updating product", { error, id, updateData });
      throw error;
    }
  }

  /**
   * Delete product (soft delete)
   */
  async deleteProduct(id: string, restaurantId?: string): Promise<void> {
    try {
      logger.info("Deleting product", { id, restaurantId });

      const product = await this.getProductById(id, restaurantId);

      await this.productRepository.softDelete(id);

      logger.info("Product deleted successfully", { productId: id });
    } catch (error) {
      logger.error("Error deleting product", { error, id });
      throw error;
    }
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(
    restaurantId: string,
    categoryId: string
  ): Promise<Product[]> {
    try {
      logger.info("Fetching products by category", { restaurantId, categoryId });

      const products = await this.productRepository.findByCategory(
        restaurantId,
        categoryId
      );

      logger.info("Products by category fetched successfully", {
        restaurantId,
        categoryId,
        count: products.length,
      });

      return products;
    } catch (error) {
      logger.error("Error fetching products by category", {
        error,
        restaurantId,
        categoryId,
      });
      throw error;
    }
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(restaurantId: string): Promise<Product[]> {
    try {
      logger.info("Fetching featured products", { restaurantId });

      const products = await this.productRepository.findFeatured(restaurantId);

      logger.info("Featured products fetched successfully", {
        restaurantId,
        count: products.length,
      });

      return products;
    } catch (error) {
      logger.error("Error fetching featured products", { error, restaurantId });
      throw error;
    }
  }

  /**
   * Get popular products
   */
  async getPopularProducts(restaurantId: string): Promise<Product[]> {
    try {
      logger.info("Fetching popular products", { restaurantId });

      const products = await this.productRepository.findPopular(restaurantId);

      logger.info("Popular products fetched successfully", {
        restaurantId,
        count: products.length,
      });

      return products;
    } catch (error) {
      logger.error("Error fetching popular products", { error, restaurantId });
      throw error;
    }
  }

  /**
   * Search products
   */
  async searchProducts(
    restaurantId: string,
    searchTerm: string,
    limit: number = 20
  ): Promise<Product[]> {
    try {
      logger.info("Searching products", { restaurantId, searchTerm, limit });

      if (!searchTerm.trim()) {
        return [];
      }

      const products = await this.productRepository.searchProducts(
        restaurantId,
        searchTerm.trim(),
        limit
      );

      logger.info("Product search completed", {
        restaurantId,
        searchTerm,
        count: products.length,
      });

      return products;
    } catch (error) {
      logger.error("Error searching products", {
        error,
        restaurantId,
        searchTerm,
      });
      throw error;
    }
  }

  /**
   * Update product availability
   */
  async updateProductAvailability(
    id: string,
    isAvailable: boolean,
    restaurantId?: string
  ): Promise<Product> {
    try {
      logger.info("Updating product availability", {
        id,
        isAvailable,
        restaurantId,
      });

      const product = await this.getProductById(id, restaurantId);

      await this.productRepository.update(id, { isAvailable });

      const updatedProduct = await this.productRepository.findByIdWithRelations(id) as Product;

      logger.info("Product availability updated successfully", {
        productId: id,
        isAvailable,
      });

      return updatedProduct;
    } catch (error) {
      logger.error("Error updating product availability", {
        error,
        id,
        isAvailable,
      });
      throw error;
    }
  }

  /**
   * Bulk update product availability
   */
  async bulkUpdateAvailability(
    productIds: string[],
    isAvailable: boolean,
    restaurantId?: string
  ): Promise<void> {
    try {
      logger.info("Bulk updating product availability", {
        productIds,
        isAvailable,
        restaurantId,
      });

      // Verify all products belong to the restaurant if restaurantId is provided
      if (restaurantId) {
        for (const productId of productIds) {
          await this.getProductById(productId, restaurantId);
        }
      }

      await this.productRepository.bulkUpdateAvailability(productIds, isAvailable);

      logger.info("Bulk product availability update completed", {
        count: productIds.length,
        isAvailable,
      });
    } catch (error) {
      logger.error("Error bulk updating product availability", {
        error,
        productIds,
        isAvailable,
      });
      throw error;
    }
  }
}
