import { Repository, SelectQueryBuilder } from "typeorm";
import { AppDataSource } from "../../database/connection";
import { BaseRepository } from "../../base/BaseRepository";
import { Product } from "./product.model";
import { ProductQueryDto } from "./product.dto";
import { PaginatedResult } from "../../base/BaseModel";

export class ProductRepository extends BaseRepository<Product> {
  private productRepository: Repository<Product>;

  constructor() {
    super(Product);
    this.productRepository = AppDataSource.getRepository(Product);
  }

  /**
   * Find products with filtering, searching, and pagination
   */
  async findProducts(
    restaurantId: string,
    query: ProductQueryDto
  ): Promise<PaginatedResult<Product>> {
    const {
      page = 1,
      limit = 10,
      search,
      categoryId,
      isAvailable,
      isFeatured,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = query;

    let queryBuilder = this.productRepository
      .createQueryBuilder("product")
      .leftJoinAndSelect("product.category", "category")
      .leftJoinAndSelect("product.restaurant", "restaurant")
      .where("product.restaurantId = :restaurantId", { restaurantId })
      .andWhere("product.deletedAt IS NULL");

    // Apply filters
    if (search) {
      queryBuilder = queryBuilder.andWhere(
        "(LOWER(product.name) LIKE LOWER(:search) OR LOWER(product.description) LIKE LOWER(:search))",
        { search: `%${search}%` }
      );
    }

    if (categoryId) {
      queryBuilder = queryBuilder.andWhere("product.categoryId = :categoryId", {
        categoryId,
      });
    }

    if (typeof isAvailable === "boolean") {
      queryBuilder = queryBuilder.andWhere("product.isAvailable = :isAvailable", {
        isAvailable,
      });
    }

    if (typeof isFeatured === "boolean") {
      queryBuilder = queryBuilder.andWhere("product.isFeatured = :isFeatured", {
        isFeatured,
      });
    }

    // Apply sorting
    const validSortFields = [
      "name",
      "price",
      "rating",
      "reviewsCount",
      "createdAt",
      "updatedAt",
      "sortOrder",
    ];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "createdAt";
    queryBuilder = queryBuilder.orderBy(
      `product.${sortField}`,
      sortOrder.toUpperCase() as "ASC" | "DESC"
    );

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder = queryBuilder.skip(offset).take(limit);

    // Execute query
    const [products, total] = await queryBuilder.getManyAndCount();

    return {
      data: products,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Find products by category
   */
  async findByCategory(
    restaurantId: string,
    categoryId: string
  ): Promise<Product[]> {
    return this.productRepository.find({
      where: {
        restaurantId,
        categoryId,
        isAvailable: true,
        deletedAt: null,
      },
      relations: ["category", "restaurant"],
      order: {
        sortOrder: "ASC",
        name: "ASC",
      },
    });
  }

  /**
   * Find featured products
   */
  async findFeatured(restaurantId: string): Promise<Product[]> {
    return this.productRepository.find({
      where: {
        restaurantId,
        isFeatured: true,
        isAvailable: true,
        deletedAt: null,
      },
      relations: ["category", "restaurant"],
      order: {
        sortOrder: "ASC",
        rating: "DESC",
      },
      take: 10,
    });
  }

  /**
   * Find popular products (high rating and review count)
   */
  async findPopular(restaurantId: string): Promise<Product[]> {
    return this.productRepository
      .createQueryBuilder("product")
      .leftJoinAndSelect("product.category", "category")
      .leftJoinAndSelect("product.restaurant", "restaurant")
      .where("product.restaurantId = :restaurantId", { restaurantId })
      .andWhere("product.isAvailable = :isAvailable", { isAvailable: true })
      .andWhere("product.deletedAt IS NULL")
      .andWhere("product.rating >= :minRating", { minRating: 4.0 })
      .andWhere("product.reviewsCount >= :minReviews", { minReviews: 10 })
      .orderBy("product.rating", "DESC")
      .addOrderBy("product.reviewsCount", "DESC")
      .take(10)
      .getMany();
  }

  /**
   * Search products by name or description
   */
  async searchProducts(
    restaurantId: string,
    searchTerm: string,
    limit: number = 20
  ): Promise<Product[]> {
    return this.productRepository
      .createQueryBuilder("product")
      .leftJoinAndSelect("product.category", "category")
      .leftJoinAndSelect("product.restaurant", "restaurant")
      .where("product.restaurantId = :restaurantId", { restaurantId })
      .andWhere("product.isAvailable = :isAvailable", { isAvailable: true })
      .andWhere("product.deletedAt IS NULL")
      .andWhere(
        "(LOWER(product.name) LIKE LOWER(:search) OR LOWER(product.description) LIKE LOWER(:search))",
        { search: `%${searchTerm}%` }
      )
      .orderBy("product.name", "ASC")
      .take(limit)
      .getMany();
  }

  /**
   * Find product by ID with relations
   */
  async findByIdWithRelations(id: string): Promise<Product | null> {
    return this.productRepository.findOne({
      where: { id, deletedAt: null },
      relations: ["category", "restaurant", "orderItems"],
    });
  }

  /**
   * Update product rating
   */
  async updateRating(id: string, rating: number, reviewsCount: number): Promise<void> {
    await this.productRepository.update(id, {
      rating,
      reviewsCount,
    });
  }

  /**
   * Get products count by restaurant
   */
  async getProductsCount(restaurantId: string): Promise<number> {
    return this.productRepository.count({
      where: {
        restaurantId,
        deletedAt: null,
      },
    });
  }

  /**
   * Get available products count by restaurant
   */
  async getAvailableProductsCount(restaurantId: string): Promise<number> {
    return this.productRepository.count({
      where: {
        restaurantId,
        isAvailable: true,
        deletedAt: null,
      },
    });
  }

  /**
   * Bulk update product availability
   */
  async bulkUpdateAvailability(
    productIds: string[],
    isAvailable: boolean
  ): Promise<void> {
    await this.productRepository
      .createQueryBuilder()
      .update(Product)
      .set({ isAvailable })
      .where("id IN (:...productIds)", { productIds })
      .execute();
  }

  /**
   * Get products by tags
   */
  async findByTags(
    restaurantId: string,
    tags: string[]
  ): Promise<Product[]> {
    return this.productRepository
      .createQueryBuilder("product")
      .leftJoinAndSelect("product.category", "category")
      .leftJoinAndSelect("product.restaurant", "restaurant")
      .where("product.restaurantId = :restaurantId", { restaurantId })
      .andWhere("product.isAvailable = :isAvailable", { isAvailable: true })
      .andWhere("product.deletedAt IS NULL")
      .andWhere("product.tags && :tags", { tags })
      .orderBy("product.name", "ASC")
      .getMany();
  }
}
