import { BrowserRouter as Router, Routes, Route } from "react-router-dom";

import ProductDetailPage from "./pages/ProductDetail";
import CustomizableProductPage from "./pages/CustomizableProductPage";
import CategoryDetailPage from "./pages/CategoryDetailPage";
import HomePage from "./pages/HomePage";
import MenuHomePage from "./pages/MenuHomePage";
import CheckoutPage from "./pages/CheckoutPage";
import OrderConfirmationPage from "./pages/OrderConfirmationPage";
import OrderStatusPage from "./pages/OrderStatusPage";

export default function App() {
  return (
    <Router>
      <div className="h-full overflow-y-auto">
        <Routes>
          <Route path="/" element={<MenuHomePage />} />
          <Route path="/menu" element={<HomePage />} />
          <Route path="/product/:id" element={<ProductDetailPage />} />
          <Route path="/customize/:id" element={<CustomizableProductPage />} />
          <Route path="/category/:category" element={<CategoryDetailPage />} />
          <Route path="/checkout" element={<CheckoutPage />} />
          <Route
            path="/order-confirmation"
            element={<OrderConfirmationPage />}
          />
          <Route path="/order-status" element={<OrderStatusPage />} />
        </Routes>
      </div>
    </Router>
  );
}
