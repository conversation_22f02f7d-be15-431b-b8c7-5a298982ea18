import React from "react";
import { useAppSelector, useAppDispatch } from "../hooks/redux";
import {
  selectCartItems,
  selectCartTotalItems,
  selectCartTotalPrice,
  selectCartIsOpen,
  closeCart,
  incrementQuantity,
  decrementQuantity,
  removeFromCart,
} from "../store/slices/cartSlice";
import { useNavigate } from "react-router-dom";
import { CartItem } from "../features/menu/types/menuTypes";
import { XIcon } from "../assets/icons/component/XIcon";
import { MinusIcon } from "../assets/icons/component/MinusIcon";
import { PlusIcon } from "../assets/icons/component/PlusIcon";
import { TrashIcon } from "../assets/icons/component/TrashIcon";

const Cart: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const items = useAppSelector(selectCartItems);
  const totalItems = useAppSelector(selectCartTotalItems);
  const totalPrice = useAppSelector(selectCartTotalPrice);
  const isOpen = useAppSelector(selectCartIsOpen);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={() => dispatch(closeCart())}
      />

      {/* Cart Panel */}
      <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-xl">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between border-b p-4">
            <h2 className="text-lg font-semibold text-secondary-800">
              Cart ({totalItems})
            </h2>
            <button
              onClick={() => dispatch(closeCart())}
              className="p-2 text-secondary-400 hover:text-secondary-600 transition-colors"
            >
              <XIcon />
            </button>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-4">
            {items.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <div className="text-secondary-400 mb-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-16 w-16 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <p className="text-secondary-600 text-lg font-medium">
                  Your cart is empty
                </p>
                <p className="text-secondary-400 text-sm mt-1">
                  Add some delicious items to get started!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {items.map((item: CartItem) => (
                  <div
                    key={item.id}
                    className="flex items-center space-x-4 bg-secondary-50 rounded-lg p-3"
                  >
                    <img
                      src={item.product.image}
                      alt={item.product.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="font-medium text-secondary-800 text-sm">
                        {item.product.name}
                      </h3>
                      <p className="text-secondary-500 text-xs">
                        {item.product.category}
                      </p>
                      <p className="font-bold text-primary-600 text-sm">
                        {item.product.price} DT
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-1 bg-white rounded-full p-1">
                        <button
                          onClick={() => dispatch(decrementQuantity(item.id))}
                          className="p-1 rounded-full hover:bg-secondary-100 transition-colors"
                        >
                          <MinusIcon />
                        </button>
                        <span className="font-medium w-6 text-center text-sm">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => dispatch(incrementQuantity(item.id))}
                          className="p-1 rounded-full hover:bg-primary-100 text-primary-600 transition-colors"
                        >
                          <PlusIcon />
                        </button>
                      </div>
                      {/* Remove Button */}
                      <button
                        onClick={() => dispatch(removeFromCart(item.id))}
                        className="p-2 text-red-400 hover:text-red-600 transition-colors"
                      >
                        <TrashIcon />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {items.length > 0 && (
            <div className="border-t p-4 space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-secondary-800">
                  Total:
                </span>
                <span className="text-xl font-bold text-primary-600">
                  {totalPrice.toFixed(2)} DT
                </span>
              </div>
              <button
                onClick={() => {
                  dispatch(closeCart());
                  navigate("/checkout");
                }}
                className="w-full bg-primary-500 text-white py-3 rounded-lg font-semibold hover:bg-primary-600 transition-colors"
              >
                Checkout ({totalItems} items)
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Cart;
