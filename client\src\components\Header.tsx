import { CartIcon } from "../assets/icons/component/CartIcon";
import { UserIcon } from "../assets/icons/component/UserIcon";
import { useAppDispatch, useAppSelector } from "../hooks/redux";
import { selectCartTotalItems, toggleCart } from "../store/slices/cartSlice";

export const Header: React.FC = () => {
  const dispatch = useAppDispatch();
  const cartItemCount = useAppSelector(selectCartTotalItems);

  return (
    <header className="flex justify-between items-center p-4 bg-white">
      <div className="w-20">
        <img
          src="https://placehold.co/100x40/FFFFFF/000000?text=Logo"
          alt="Logo"
        />
      </div>
      <div className="flex items-center space-x-4">
        <UserIcon />
        <div onClick={() => dispatch(toggleCart())}>
          <CartIcon itemCount={cartItemCount} />
        </div>
      </div>
    </header>
  );
};
