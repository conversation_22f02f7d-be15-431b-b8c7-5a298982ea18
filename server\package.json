{"name": "server", "version": "1.0.0", "description": "Node.js server with Express, TypeScript, and TypeORM", "main": "dist/server.js", "scripts": {"start": "node -r tsconfig-paths/register dist/server.js", "dev": "nodemon --exec ts-node -r tsconfig-paths/register src/server.ts", "build": "tsc -p .", "typeorm": "typeorm-ts-node-commonjs -d src/database/connection.ts", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert"}, "keywords": ["express", "typescript", "typeorm", "postgres"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^1.20.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.19.2", "http": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "pg": "^8.11.5", "reflect-metadata": "^0.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsconfig-paths": "^4.2.0", "typeorm": "^0.3.20", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.12.7", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}