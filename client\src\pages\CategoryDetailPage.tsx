import React, { useMemo } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useAppDispatch } from "../hooks/redux";
import { addToCart } from "../store/slices/cartSlice";
import { getProductsByCategory } from "../data/products";

// Icons
const ArrowLeftIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
  </svg>
);

const CartIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9" />
  </svg>
);

const PlusIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
  </svg>
);

const StarIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 fill-current" viewBox="0 0 24 24">
    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
  </svg>
);

interface CategoryDetailPageProps {}

const CategoryDetailPage: React.FC<CategoryDetailPageProps> = () => {
  const { category } = useParams<{ category: string }>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const products = useMemo(() => {
    if (!category) return [];
    return getProductsByCategory(category);
  }, [category]);

  if (!category) {
    return (
      <div className="bg-secondary-50 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-secondary-800 mb-4">Category Not Found</h2>
          <button
            onClick={() => navigate("/")}
            className="bg-primary-500 text-white px-6 py-2 rounded-lg hover:bg-primary-600 transition-colors"
          >
            Go Back Home
          </button>
        </div>
      </div>
    );
  }

  const handleAddToCart = (product: any) => {
    dispatch(addToCart({ product, quantity: 1 }));
  };

  const handleProductClick = (productId: number) => {
    navigate(`/product/${productId}`);
  };

  return (
    <div className="bg-secondary-50 min-h-screen font-sans">
      <div className="max-w-md mx-auto bg-white shadow-lg">
        {/* Header */}
        <div className="flex justify-between items-center p-4 bg-white border-b border-secondary-100">
          <button
            onClick={() => navigate("/")}
            className="p-2 rounded-full hover:bg-secondary-100 transition-colors"
          >
            <ArrowLeftIcon />
          </button>
          <h1 className="text-lg font-semibold text-secondary-800 capitalize">
            {category}
          </h1>
          <div className="p-2">
            <CartIcon />
          </div>
        </div>

        {/* Products Grid */}
        <div className="p-4">
          {products.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-secondary-500 text-lg">No products found in this category</p>
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <div
                  key={product.id}
                  className="bg-white rounded-xl shadow-sm border border-secondary-100 overflow-hidden hover:shadow-md transition-shadow"
                >
                  {/* Product Image */}
                  <div 
                    className="cursor-pointer"
                    onClick={() => handleProductClick(product.id)}
                  >
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-48 object-cover"
                    />
                  </div>

                  {/* Product Info */}
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h3 
                          className="font-semibold text-lg text-secondary-800 cursor-pointer hover:text-primary-600 transition-colors"
                          onClick={() => handleProductClick(product.id)}
                        >
                          {product.name}
                        </h3>
                        <p className="text-sm text-secondary-500 mb-2">{product.category}</p>
                        
                        {/* Rating */}
                        {product.rating && (
                          <div className="flex items-center space-x-1 mb-2">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <StarIcon
                                  key={i}
                                  className={`h-4 w-4 ${
                                    i < Math.floor(product.rating!)
                                      ? "text-yellow-400"
                                      : "text-secondary-300"
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="text-sm text-secondary-600">
                              {product.rating} ({product.reviews} reviews)
                            </span>
                          </div>
                        )}

                        {/* Description */}
                        {product.description && (
                          <p className="text-sm text-secondary-600 line-clamp-2 mb-3">
                            {product.description}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Price and Add to Cart */}
                    <div className="flex justify-between items-center">
                      <div className="text-xl font-bold text-primary-600">
                        {product.price} DT
                      </div>
                      <button
                        onClick={() => handleAddToCart(product)}
                        className="bg-primary-500 text-white rounded-full p-3 hover:bg-primary-600 transition-colors flex items-center justify-center"
                      >
                        <PlusIcon />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Bottom spacing */}
        <div className="h-20"></div>
      </div>
    </div>
  );
};

export default CategoryDetailPage;
