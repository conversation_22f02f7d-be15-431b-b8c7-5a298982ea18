import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from "typeorm";
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsNumber,
  Min,
  IsEnum,
  IsUUID,
} from "class-validator";
import { Restaurant } from "../restaurant/restaurant.model";
import { Table } from "../table/table.model";
import { User } from "../user/user.model";
import { OrderItem } from "./order-item.model";

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PREPARING = 'preparing',
  READY = 'ready',
  SERVED = 'served',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum OrderType {
  DINE_IN = 'dine_in',
  TAKEAWAY = 'takeaway',
  DELIVERY = 'delivery'
}

@Entity("orders")
@Index(["orderNumber"], { unique: true })
export class Order {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 50, unique: true })
  @IsNotEmpty({ message: "Order number is required" })
  @IsString({ message: "Order number must be a string" })
  orderNumber: string;

  @Column({
    type: "enum",
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  @IsEnum(OrderStatus, { message: "Invalid order status" })
  status: OrderStatus;

  @Column({
    type: "enum",
    enum: OrderType,
    default: OrderType.DINE_IN,
  })
  @IsEnum(OrderType, { message: "Invalid order type" })
  type: OrderType;

  @Column({ type: "decimal", precision: 10, scale: 2 })
  @IsNotEmpty({ message: "Subtotal is required" })
  @IsNumber({}, { message: "Subtotal must be a number" })
  @Min(0, { message: "Subtotal must be non-negative" })
  subtotal: number;

  @Column({ type: "decimal", precision: 10, scale: 2, default: 0 })
  @IsOptional()
  @IsNumber({}, { message: "Tax amount must be a number" })
  @Min(0, { message: "Tax amount must be non-negative" })
  taxAmount: number;

  @Column({ type: "decimal", precision: 10, scale: 2, default: 0 })
  @IsOptional()
  @IsNumber({}, { message: "Service charge must be a number" })
  @Min(0, { message: "Service charge must be non-negative" })
  serviceCharge: number;

  @Column({ type: "decimal", precision: 10, scale: 2, default: 0 })
  @IsOptional()
  @IsNumber({}, { message: "Discount amount must be a number" })
  @Min(0, { message: "Discount amount must be non-negative" })
  discountAmount: number;

  @Column({ type: "decimal", precision: 10, scale: 2 })
  @IsNotEmpty({ message: "Total amount is required" })
  @IsNumber({}, { message: "Total amount must be a number" })
  @Min(0, { message: "Total amount must be non-negative" })
  totalAmount: number;

  @Column({ type: "text", nullable: true })
  @IsOptional()
  @IsString({ message: "Special instructions must be a string" })
  specialInstructions?: string;

  @Column({ type: "timestamp", nullable: true })
  estimatedReadyTime?: Date;

  @Column({ type: "timestamp", nullable: true })
  servedAt?: Date;

  @Column({ type: "timestamp", nullable: true })
  completedAt?: Date;

  @Column({ type: "varchar", length: 255, nullable: true })
  @IsOptional()
  @IsString({ message: "Customer name must be a string" })
  customerName?: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  @IsOptional()
  @IsString({ message: "Customer phone must be a string" })
  customerPhone?: string;

  // Relationships
  @Column({ type: "uuid" })
  @IsUUID(4, { message: "Restaurant ID must be a valid UUID" })
  restaurantId: string;

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.orders)
  @JoinColumn({ name: "restaurantId" })
  restaurant: Restaurant;

  @Column({ type: "uuid", nullable: true })
  @IsOptional()
  @IsUUID(4, { message: "Table ID must be a valid UUID" })
  tableId?: string;

  @ManyToOne(() => Table, (table) => table.orders, { nullable: true })
  @JoinColumn({ name: "tableId" })
  table?: Table;

  @Column({ type: "uuid", nullable: true })
  @IsOptional()
  @IsUUID(4, { message: "Customer ID must be a valid UUID" })
  customerId?: string;

  @ManyToOne(() => User, (user) => user.customerOrders, { nullable: true })
  @JoinColumn({ name: "customerId" })
  customer?: User;

  @Column({ type: "uuid", nullable: true })
  @IsOptional()
  @IsUUID(4, { message: "Waiter ID must be a valid UUID" })
  waiterId?: string;

  @ManyToOne(() => User, (user) => user.waiterOrders, { nullable: true })
  @JoinColumn({ name: "waiterId" })
  waiter?: User;

  @OneToMany(() => OrderItem, (orderItem) => orderItem.order, { cascade: true })
  items: OrderItem[];

  // Timestamps
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  // Virtual properties
  get totalItems(): number {
    return this.items?.reduce((sum, item) => sum + item.quantity, 0) || 0;
  }

  get isActive(): boolean {
    return ![OrderStatus.COMPLETED, OrderStatus.CANCELLED].includes(this.status);
  }

  get canBeCancelled(): boolean {
    return [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(this.status);
  }

  // Methods
  calculateTotals(): void {
    this.subtotal = this.items?.reduce((sum, item) => sum + item.totalPrice, 0) || 0;
    this.totalAmount = this.subtotal + this.taxAmount + this.serviceCharge - this.discountAmount;
  }

  static generateOrderNumber(): string {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD-${timestamp}${random}`;
  }
}
