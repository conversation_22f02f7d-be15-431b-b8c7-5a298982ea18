import { Product } from "../types/menuTypes";
import { MinusIcon } from "../../../assets/icons/component/MinusIcon";
import { PlusIcon } from "../../../assets/icons/component/PlusIcon";
import {
  addToCart,
  decrementQuantity,
  incrementQuantity,
  selectCartItemById,
} from "../../../store/slices/cartSlice";
import { Link } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../hooks/redux";

export const BestOfTodayItem: React.FC<{ item: Product }> = ({ item }) => {
  const dispatch = useAppDispatch();
  const cartItem = useAppSelector((state) =>
    selectCartItemById(state, item.id)
  );
  const quantity = cartItem?.quantity || 0;

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    if (quantity === 0) {
      dispatch(addToCart({ product: item, quantity: 1 }));
    }
  };

  const handleIncrement = (e: React.MouseEvent) => {
    e.preventDefault();
    if (quantity === 0) {
      dispatch(addToCart({ product: item, quantity: 1 }));
    } else {
      dispatch(incrementQuantity(item.id));
    }
  };

  const handleDecrement = (e: React.MouseEvent) => {
    e.preventDefault();
    dispatch(decrementQuantity(item.id));
  };

  return (
    <Link to={`/product/${item.id}`} className="block">
      <div className="flex items-center bg-white p-3 rounded-xl shadow-sm mb-4 hover:shadow-md transition-shadow">
        <img
          src={item.image}
          alt={item.name}
          className="w-20 h-20 rounded-lg object-cover"
        />
        <div className="flex-grow ml-4">
          <h3 className="font-semibold text-secondary-800">{item.name}</h3>
          <p className="text-sm text-secondary-500">{item.category}</p>
          <p className="font-bold text-secondary-800 mt-1">{item.price} DT</p>
        </div>
        <div className="flex items-center space-x-2 bg-secondary-100 rounded-full p-1">
          {quantity > 0 ? (
            <>
              <button
                onClick={handleDecrement}
                className="p-1 rounded-full bg-white text-secondary-600"
              >
                <MinusIcon />
              </button>
              <span className="font-bold w-4 text-center">{quantity}</span>
              <button
                onClick={handleIncrement}
                className="p-1 rounded-full bg-white text-primary-500"
              >
                <PlusIcon />
              </button>
            </>
          ) : (
            <button
              onClick={handleAddToCart}
              className="p-2 rounded-full bg-primary-500 text-white hover:bg-primary-600 transition-colors"
            >
              <PlusIcon />
            </button>
          )}
        </div>
      </div>
    </Link>
  );
};
