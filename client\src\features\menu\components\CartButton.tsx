import { Product } from "../types/menuTypes";

export default function CartButton({
  product,
  quantity,
}: {
  product: Product;
  quantity: number;
}) {
  return (
    <button className="bg-primary-500 text-white px-6 py-3 rounded-xl font-semibold hover:bg-primary-600 transition-colors flex items-center justify-between">
      <span>Add to Cart</span>
      <span className="bg-primary-600 px-2 py-1 rounded-full text-sm">
        {(product.price * quantity).toFixed(0)} DT
      </span>
    </button>
  );
}
