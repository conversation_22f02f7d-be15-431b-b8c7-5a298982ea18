import {
  BaseModel,
  PaginatedResult,
  <PERSON><PERSON>ationO<PERSON>s,
  PaginationMeta,
} from "./BaseModel";

export abstract class BaseRepository<
  T extends BaseModel,
  CreateData,
  UpdateData
> {
  protected abstract tableName: string;

  abstract create(data: CreateData): Promise<T>;
  abstract findById(id: string): Promise<T | null>;
  abstract findMany(options?: PaginationOptions): Promise<PaginatedResult<T>>;
  abstract update(id: string, data: UpdateData): Promise<T | null>;
  abstract delete(id: string): Promise<boolean>;
  abstract softDelete(id: string): Promise<boolean>;
  abstract exists(id: string): Promise<boolean>;

  protected createPaginationMeta(
    page: number,
    limit: number,
    total: number
  ): PaginationMeta {
    const totalPages = Math.ceil(total / limit);
    return {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  protected getOffset(page: number, limit: number): number {
    return (page - 1) * limit;
  }
}
