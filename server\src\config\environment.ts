import dotenv from "dotenv";

// Load environment variables from .env file
dotenv.config();

export const env = {
  // Application environment
  nodeEnv: process.env.NODE_ENV || "development",
  port: Number(process.env.PORT) || 5000,

  // Database configuration
  db: {
    host: process.env.DB_HOST || "localhost",
    port: Number(process.env.DB_PORT) || 5432,
    username: process.env.DB_USERNAME || "postgres",
    password: process.env.DB_PASSWORD || "password",
    database: process.env.DB_NAME || "mydatabase",
  },

  // JWT configuration
  jwt: {
    secret:
      process.env.JWT_SECRET ||
      "your-super-secret-jwt-key-change-this-in-production",
    expiresIn: process.env.JWT_EXPIRES_IN || "7d",
    cookieName: "auth_token",
  },

  // Cookie configuration
  cookie: {
    secret:
      process.env.COOKIE_SECRET ||
      "your-super-secret-cookie-key-change-this-in-production",
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict" as const,
  },

  // Logger configuration
  log: {
    level: process.env.LOG_LEVEL || "info",
  },

  // API Documentation
  swagger: {
    title: "QR Code Menu App Documentation",
    version: "1.0.0",
    description: "API documentation for QR Code Menu App",
  },
};
