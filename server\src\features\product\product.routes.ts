import { Router } from "express";
import { ProductController } from "./product.controller";
import { AuthMiddleware } from "../../middleware/auth.middleware";
import { ValidationMiddleware } from "../../middleware/validation.middleware";
import { 
  requireOwnerOrAdmin, 
  requireStaff, 
  requireRestaurantAccess 
} from "../../middleware/role.middleware";
import { CreateProductDto, UpdateProductDto } from "./product.dto";

const router = Router();
const productController = new ProductController();

/**
 * Public routes (no authentication required)
 */

// Get products with filtering and pagination
router.get("/", productController.getProducts);

// Get product by ID
router.get("/:id", productController.getProductById);

// Get products by category
router.get("/category/:categoryId", productController.getProductsByCategory);

// Get featured products
router.get("/featured", productController.getFeaturedProducts);

// Search products
router.get("/search", productController.searchProducts);

/**
 * Protected routes (authentication required)
 */

// Create product (Admin or Restaurant Owner only)
router.post(
  "/",
  AuthMiddleware.authenticate,
  requireOwnerOrAdmin,
  requireRestaurantAccess,
  ValidationMiddleware.validate(CreateProductDto),
  productController.createProduct
);

// Update product (Admin or Restaurant Owner only)
router.put(
  "/:id",
  AuthMiddleware.authenticate,
  requireOwnerOrAdmin,
  requireRestaurantAccess,
  ValidationMiddleware.validate(UpdateProductDto),
  productController.updateProduct
);

// Delete product (Admin or Restaurant Owner only)
router.delete(
  "/:id",
  AuthMiddleware.authenticate,
  requireOwnerOrAdmin,
  requireRestaurantAccess,
  productController.deleteProduct
);

// Update product availability (Staff only)
router.patch(
  "/:id/availability",
  AuthMiddleware.authenticate,
  requireStaff,
  requireRestaurantAccess,
  productController.updateProductAvailability
);

export { router as productRoutes };
