import React, { useMemo } from "react";

import { useAppSelector } from "../hooks/redux";
import {
  selectSelectedCategory,
  selectSearchQuery,
} from "../store/slices/filterSlice";
import { getProductsByCategory } from "../data/products";
import Cart from "../components/Cart";
import { ProductCard } from "../features/menu/components/ProductCard";
import { Categories } from "../features/menu/components/Categories";
import { BestOfToday } from "../features/menu/components/BestOfToday";
import { Banner } from "../components/Banner";
import { SearchBar } from "../components/SearchBar";
import { Header } from "../components/Header";

const HomePage: React.FC = () => {
  const selectedCategory = useAppSelector(selectSelectedCategory);
  const searchQuery = useAppSelector(selectSearchQuery);

  // Filter products based on category and search query
  const filteredProducts = useMemo(() => {
    let filtered = getProductsByCategory(selectedCategory);

    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  }, [selectedCategory, searchQuery]);

  return (
    <div className="bg-secondary-50 min-h-screen font-sans">
      <div className="max-w-md mx-auto bg-white shadow-lg">
        <Header />
        <SearchBar />
        <Banner />
        <BestOfToday />
        <Categories />
        <div className="p-4 grid grid-cols-2 gap-4">
          {filteredProducts.map((product) => (
            <ProductCard key={product?.id} product={product} />
          ))}
        </div>
        <Cart />
      </div>
    </div>
  );
};

export default HomePage;
