import logger from "@/config/logger";

export abstract class BaseService {
  protected abstract serviceName: string;

  protected logError(error: any, operation: string): void {
    logger.error(`${this.serviceName} - ${operation}:`, {
      error: error.message,
      stack: error.stack,
    });
  }

  protected handleError(error: any, operation: string): never {
    this.logError(error, operation);
    throw error;
  }
}
