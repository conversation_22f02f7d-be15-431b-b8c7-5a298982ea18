import { Request, Response, NextFunction } from "express";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";
import { RegisterDto, LoginDto } from "./auth.dto";
import { ApiError } from "../../utils/ApiError";

export class AuthValidator {
  static validateRegister = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const registerDto = plainToClass(RegisterDto, req.body);
      const errors = await validate(registerDto);

      if (errors.length > 0) {
        const errorMessages = errors
          .map((error) => Object.values(error.constraints || {}))
          .flat();
        throw new ApiError(400, "Validation failed", errorMessages);
      }

      req.body = registerDto;
      next();
    } catch (error) {
      next(error);
    }
  };

  static validateLogin = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const loginDto = plainToClass(LoginDto, req.body);
      const errors = await validate(loginDto);

      if (errors.length > 0) {
        const errorMessages = errors
          .map((error) => Object.values(error.constraints || {}))
          .flat();
        throw new ApiError(400, "Validation failed", errorMessages);
      }

      req.body = loginDto;
      next();
    } catch (error) {
      next(error);
    }
  };
}
