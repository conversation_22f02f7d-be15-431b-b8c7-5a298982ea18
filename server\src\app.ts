import express, { Application } from "express";
import { json, urlencoded } from "body-parser";
import cors from "cors";
import { setupSwagger } from "./config/swagger";
import { ErrorMiddleware } from "./middleware/error.middleware";

export class App {
  public express: Application;

  constructor() {
    this.express = express();
    this.middleware();
    this.routes();
    this.errorHandling();
  }

  private middleware(): void {
    this.express.use(cors());
    this.express.use(json());
    this.express.use(urlencoded({ extended: true }));
    this.express.use(
      require("cookie-parser")(
        require("./config/environment").env.cookie.secret
      )
    );
  }

  private routes(): void {
    // Health check route
    setupSwagger(this.express);

    // API routes
    this.express.use(
      "/api/auth",
      require("./features/auth/auth.routes").default
    );
  }

  private errorHandling(): void {
    // Handle 404 errors
    this.express.use(ErrorMiddleware.notFound);

    // Global error handler (must be last)
    this.express.use(ErrorMiddleware.handle);
  }
}
