import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm";
import { User } from "../user/user.model";

@Entity("sessions")
@Index(["token"], { unique: true })
@Index(["userId", "isActive"])
export class Session {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "uuid" })
  userId: string;

  @Column({ type: "text", unique: true })
  token: string;

  @Column({ type: "timestamp" })
  expiresAt: Date;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @Column({ type: "varchar", length: 45, nullable: true })
  ipAddress: string;

  @Column({ type: "text", nullable: true })
  userAgent: string;

  @CreateDateColumn({ type: "timestamp" })
  createdAt: Date;

  // Relationships
  @ManyToOne(() => User, (user) => user.sessions, {
    onDelete: "CASCADE",
  })
  @Join<PERSON>olumn({ name: "userId" })
  user: User;

  // Method to check if session is expired
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  // Method to check if session is valid
  isValid(): boolean {
    return this.isActive && !this.isExpired();
  }
}
