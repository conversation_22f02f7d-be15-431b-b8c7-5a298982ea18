import { BaseService } from "../../base/BaseService";
import { Order, OrderStatus } from "./order.model";
import { OrderItem } from "./order-item.model";
import { OrderRepository } from "./order.repository";
import { ProductRepository } from "../product/product.repository";
import { CreateOrderDto, UpdateOrderStatusDto, OrderQueryDto, OrderStatsDto } from "./order.dto";
import { ApiError } from "../../utils/ApiError";
import { PaginatedResult } from "../../base/BaseModel";
import logger from "../../config/logger";
import { AppDataSource } from "../../database/connection";

export class OrderService extends BaseService<Order> {
  private orderRepository: OrderRepository;
  private productRepository: ProductRepository;

  constructor() {
    super();
    this.orderRepository = new OrderRepository();
    this.productRepository = new ProductRepository();
  }

  /**
   * Create a new order
   */
  async createOrder(orderData: CreateOrderDto, customerId: string): Promise<Order> {
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      logger.info("Creating new order", { orderData, customerId });

      // Generate order number
      const orderNumber = await this.orderRepository.generateOrderNumber(
        orderData.restaurantId
      );

      // Validate and calculate order items
      let totalAmount = 0;
      let subtotal = 0;
      const orderItems: Partial<OrderItem>[] = [];

      for (const itemData of orderData.items) {
        // Get product details
        const product = await this.productRepository.findByIdWithRelations(
          itemData.productId
        );

        if (!product) {
          throw ApiError.notFound(`Product with ID ${itemData.productId} not found`);
        }

        if (!product.isAvailable) {
          throw ApiError.badRequest(`Product "${product.name}" is not available`);
        }

        if (product.restaurantId !== orderData.restaurantId) {
          throw ApiError.badRequest(
            `Product "${product.name}" does not belong to this restaurant`
          );
        }

        // Create order item
        const orderItem = OrderItem.createFromProduct(
          product,
          itemData.quantity,
          itemData.customizations,
          itemData.specialInstructions
        );

        orderItems.push(orderItem);
        subtotal += orderItem.totalPrice!;
      }

      // Calculate taxes and fees (you can customize this logic)
      const taxRate = 0.1; // 10% tax
      const taxAmount = subtotal * taxRate;
      const serviceCharge = 0; // No service charge for now
      const deliveryFee = orderData.type === 'DELIVERY' ? 5.0 : 0;

      totalAmount = subtotal + taxAmount + serviceCharge + deliveryFee;

      // Create the order
      const order = this.orderRepository.create({
        orderNumber,
        restaurantId: orderData.restaurantId,
        customerId,
        tableId: orderData.tableId,
        type: orderData.type,
        status: OrderStatus.PENDING,
        subtotal,
        taxAmount,
        serviceCharge,
        deliveryFee,
        totalAmount,
        notes: orderData.notes,
        deliveryAddress: orderData.deliveryAddress,
        deliveryPhone: orderData.deliveryPhone,
        customerName: orderData.customerName,
      });

      const savedOrder = await queryRunner.manager.save(order);

      // Save order items
      for (const itemData of orderItems) {
        itemData.orderId = savedOrder.id;
        await queryRunner.manager.save(OrderItem, itemData);
      }

      await queryRunner.commitTransaction();

      logger.info("Order created successfully", { orderId: savedOrder.id });

      return await this.orderRepository.findByIdWithRelations(savedOrder.id) as Order;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      logger.error("Error creating order", { error, orderData, customerId });
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get orders with filtering and pagination
   */
  async getOrders(
    restaurantId: string,
    query: OrderQueryDto,
    customerId?: string
  ): Promise<PaginatedResult<Order>> {
    try {
      logger.info("Fetching orders", { restaurantId, query, customerId });

      const result = await this.orderRepository.findOrders(
        restaurantId,
        query,
        customerId
      );

      logger.info("Orders fetched successfully", {
        restaurantId,
        count: result.data.length,
        total: result.meta.total,
      });

      return result;
    } catch (error) {
      logger.error("Error fetching orders", { error, restaurantId, query });
      throw error;
    }
  }

  /**
   * Get order by ID
   */
  async getOrderById(
    id: string,
    restaurantId?: string,
    customerId?: string
  ): Promise<Order> {
    try {
      logger.info("Fetching order by ID", { id, restaurantId, customerId });

      const order = await this.orderRepository.findByIdWithRelations(id);

      if (!order) {
        throw ApiError.notFound("Order not found");
      }

      // Check restaurant access if provided
      if (restaurantId && order.restaurantId !== restaurantId) {
        throw ApiError.forbidden("Access denied to this order");
      }

      // Check customer access if provided
      if (customerId && order.customerId !== customerId) {
        throw ApiError.forbidden("Access denied to this order");
      }

      logger.info("Order fetched successfully", { orderId: id });

      return order;
    } catch (error) {
      logger.error("Error fetching order", { error, id, restaurantId, customerId });
      throw error;
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(
    id: string,
    statusData: UpdateOrderStatusDto,
    restaurantId?: string
  ): Promise<Order> {
    try {
      logger.info("Updating order status", { id, statusData, restaurantId });

      const order = await this.getOrderById(id, restaurantId);

      // Validate status transition
      this.validateStatusTransition(order.status, statusData.status);

      // Update the order status
      await this.orderRepository.updateStatus(id, statusData.status, statusData.notes);

      const updatedOrder = await this.orderRepository.findByIdWithRelations(id) as Order;

      logger.info("Order status updated successfully", {
        orderId: id,
        oldStatus: order.status,
        newStatus: statusData.status,
      });

      return updatedOrder;
    } catch (error) {
      logger.error("Error updating order status", { error, id, statusData });
      throw error;
    }
  }

  /**
   * Assign waiter to order
   */
  async assignWaiter(
    id: string,
    waiterId: string,
    restaurantId?: string
  ): Promise<Order> {
    try {
      logger.info("Assigning waiter to order", { id, waiterId, restaurantId });

      const order = await this.getOrderById(id, restaurantId);

      await this.orderRepository.assignWaiter(id, waiterId);

      const updatedOrder = await this.orderRepository.findByIdWithRelations(id) as Order;

      logger.info("Waiter assigned successfully", { orderId: id, waiterId });

      return updatedOrder;
    } catch (error) {
      logger.error("Error assigning waiter", { error, id, waiterId });
      throw error;
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(
    id: string,
    reason?: string,
    restaurantId?: string,
    customerId?: string
  ): Promise<Order> {
    try {
      logger.info("Cancelling order", { id, reason, restaurantId, customerId });

      const order = await this.getOrderById(id, restaurantId, customerId);

      // Check if order can be cancelled
      if (![OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(order.status)) {
        throw ApiError.badRequest(
          "Order cannot be cancelled in its current status"
        );
      }

      await this.orderRepository.updateStatus(
        id,
        OrderStatus.CANCELLED,
        reason || "Order cancelled"
      );

      const updatedOrder = await this.orderRepository.findByIdWithRelations(id) as Order;

      logger.info("Order cancelled successfully", { orderId: id });

      return updatedOrder;
    } catch (error) {
      logger.error("Error cancelling order", { error, id, reason });
      throw error;
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStats(
    restaurantId: string,
    fromDate?: Date,
    toDate?: Date
  ): Promise<OrderStatsDto> {
    try {
      logger.info("Fetching order statistics", { restaurantId, fromDate, toDate });

      const stats = await this.orderRepository.getOrderStats(
        restaurantId,
        fromDate,
        toDate
      );

      logger.info("Order statistics fetched successfully", { restaurantId, stats });

      return stats;
    } catch (error) {
      logger.error("Error fetching order statistics", {
        error,
        restaurantId,
        fromDate,
        toDate,
      });
      throw error;
    }
  }

  /**
   * Get orders by customer
   */
  async getCustomerOrders(customerId: string, limit: number = 20): Promise<Order[]> {
    try {
      logger.info("Fetching customer orders", { customerId, limit });

      const orders = await this.orderRepository.findByCustomer(customerId, limit);

      logger.info("Customer orders fetched successfully", {
        customerId,
        count: orders.length,
      });

      return orders;
    } catch (error) {
      logger.error("Error fetching customer orders", { error, customerId });
      throw error;
    }
  }

  /**
   * Validate status transition
   */
  private validateStatusTransition(currentStatus: OrderStatus, newStatus: OrderStatus): void {
    const validTransitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.PENDING]: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED],
      [OrderStatus.CONFIRMED]: [OrderStatus.PREPARING, OrderStatus.CANCELLED],
      [OrderStatus.PREPARING]: [OrderStatus.READY, OrderStatus.CANCELLED],
      [OrderStatus.READY]: [OrderStatus.COMPLETED, OrderStatus.CANCELLED],
      [OrderStatus.COMPLETED]: [], // No transitions from completed
      [OrderStatus.CANCELLED]: [], // No transitions from cancelled
    };

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw ApiError.badRequest(
        `Invalid status transition from ${currentStatus} to ${newStatus}`
      );
    }
  }
}
