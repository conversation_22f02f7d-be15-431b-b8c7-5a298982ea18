import { Router } from "express";
import { OrderController } from "./order.controller";
import { AuthMiddleware } from "../../middleware/auth.middleware";
import { ValidationMiddleware } from "../../middleware/validation.middleware";
import { 
  requireOwnerOrAdmin, 
  requireStaff, 
  requireOrderAccess,
  requireRole
} from "../../middleware/role.middleware";
import { CreateOrderDto, UpdateOrderStatusDto, AssignWaiterDto } from "./order.dto";
import { UserRole } from "../../types/roles.enum";

const router = Router();
const orderController = new OrderController();

/**
 * Protected routes (authentication required)
 */

// Create order (Authenticated users only)
router.post(
  "/",
  AuthMiddleware.authenticate,
  ValidationMiddleware.validate(CreateOrderDto),
  orderController.createOrder
);

// Get orders (Role-based access)
router.get(
  "/",
  AuthMiddleware.authenticate,
  requireOrderAccess('view'),
  orderController.getOrders
);

// Get order statistics (Staff only)
router.get(
  "/stats",
  AuthMiddleware.authenticate,
  requireStaff,
  orderController.getOrderStats
);

// Get current user's orders (Clients only)
router.get(
  "/my-orders",
  AuthMiddleware.authenticate,
  requireRole(UserRole.CLIENT),
  orderController.getMyOrders
);

// Get order by ID (Role-based access)
router.get(
  "/:id",
  AuthMiddleware.authenticate,
  requireOrderAccess('view'),
  orderController.getOrderById
);

// Update order status (Staff only)
router.patch(
  "/:id/status",
  AuthMiddleware.authenticate,
  requireStaff,
  ValidationMiddleware.validate(UpdateOrderStatusDto),
  orderController.updateOrderStatus
);

// Assign waiter to order (Restaurant Owner or Admin only)
router.patch(
  "/:id/assign-waiter",
  AuthMiddleware.authenticate,
  requireOwnerOrAdmin,
  ValidationMiddleware.validate(AssignWaiterDto),
  orderController.assignWaiter
);

// Cancel order (Role-based access)
router.patch(
  "/:id/cancel",
  AuthMiddleware.authenticate,
  requireOrderAccess('manage'),
  orderController.cancelOrder
);

export { router as orderRoutes };
