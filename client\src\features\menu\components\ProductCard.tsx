import { Link } from "react-router-dom";
import { PlusIcon } from "../../../assets/icons/component/PlusIcon";
import { useAppDispatch } from "../../../hooks/redux";
import { addToCart } from "../../../store/slices/cartSlice";
import { Product } from "../types/menuTypes";

export const ProductCard: React.FC<{ product: Product }> = ({ product }) => {
  const dispatch = useAppDispatch();

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    dispatch(addToCart({ product, quantity: 1 }));
  };

  return (
    <Link to={`/product/${product.id}`} className="block">
      <div className="bg-white rounded-xl shadow-sm p-3 relative hover:shadow-md transition-shadow">
        <img
          src={product.image}
          alt={product.name}
          className="w-full h-32 rounded-lg object-cover mb-3"
        />
        <h3 className="font-semibold text-secondary-800">{product.name}</h3>
        <p className="text-sm text-secondary-500">{product.category}</p>
        <div className="flex justify-between items-center mt-2">
          <p className="font-bold text-lg">{product.price} DT</p>
          <button
            onClick={handleAddToCart}
            className="bg-primary-500 text-white rounded-full p-2 w-10 h-10 flex items-center justify-center hover:bg-primary-600 transition-colors"
          >
            <PlusIcon />
          </button>
        </div>
      </div>
    </Link>
  );
};
