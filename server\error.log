{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m22\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'email' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m22\u001b[0m   email: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m27\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'password' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m27\u001b[0m   password: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m31\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'firstName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m31\u001b[0m   firstName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'lastName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   lastName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m41\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'updatedAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m44\u001b[0m   updatedAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m51\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'sessions' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m51\u001b[0m   sessions: Session[];\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m22\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'email' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m22\u001b[0m   email: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m27\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'password' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m27\u001b[0m   password: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m31\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'firstName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m31\u001b[0m   firstName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'lastName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   lastName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m41\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'updatedAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m44\u001b[0m   updatedAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m51\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'sessions' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m51\u001b[0m   sessions: Session[];\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m22\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'email' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m22\u001b[0m   email: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m27\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'password' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m27\u001b[0m   password: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m31\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'firstName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m31\u001b[0m   firstName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'lastName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   lastName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m41\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'updatedAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m44\u001b[0m   updatedAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m51\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'sessions' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m51\u001b[0m   sessions: Session[];\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m22\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'email' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m22\u001b[0m   email: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m27\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'password' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m27\u001b[0m   password: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m31\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'firstName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m31\u001b[0m   firstName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'lastName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   lastName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m41\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'updatedAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m44\u001b[0m   updatedAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m51\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'sessions' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m51\u001b[0m   sessions: Session[];\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m22\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'email' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m22\u001b[0m   email: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m27\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'password' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m27\u001b[0m   password: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m31\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'firstName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m31\u001b[0m   firstName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'lastName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   lastName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m41\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'updatedAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m44\u001b[0m   updatedAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m51\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'sessions' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m51\u001b[0m   sessions: Session[];\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m22\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'email' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m22\u001b[0m   email: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m27\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'password' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m27\u001b[0m   password: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m31\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'firstName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m31\u001b[0m   firstName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'lastName' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   lastName: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m41\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'updatedAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m44\u001b[0m   updatedAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m51\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'sessions' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m51\u001b[0m   sessions: Session[];\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2564,2564,2564,2564,2564,2564,2564,2564,2564],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m17\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'id' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m17\u001b[0m   id: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userId' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m20\u001b[0m   userId: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'token' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m23\u001b[0m   token: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m26\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'expiresAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m26\u001b[0m   expiresAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m29\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'isActive' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m29\u001b[0m   isActive: boolean;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m32\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'ipAddress' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m32\u001b[0m   ipAddress: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'userAgent' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m35\u001b[0m   userAgent: string;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m38\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'createdAt' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m38\u001b[0m   createdAt: Date;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/session.entity.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m3\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2564: \u001b[0mProperty 'user' has no initializer and is not definitely assigned in the constructor.\r\n\r\n\u001b[7m45\u001b[0m   user: User;\r\n\u001b[7m  \u001b[0m \u001b[91m  ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2345],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/database/migrations/1754923810374-CreateUserAndSessionTables.ts\u001b[0m:\u001b[93m143\u001b[0m:\u001b[93m52\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ columnNames: string[]; referencedColumnNames: string[]; referencedTableName: string; onDelete: string; name: string; }' is not assignable to parameter of type 'TableForeignKey'.\r\n  Type '{ columnNames: string[]; referencedColumnNames: string[]; referencedTableName: string; onDelete: string; name: string; }' is missing the following properties from type 'TableForeignKey': \"@instanceof\", clone\r\n\r\n\u001b[7m143\u001b[0m     await queryRunner.createForeignKey(\"sessions\", {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                   ~\u001b[0m\r\n\u001b[7m144\u001b[0m       columnNames: [\"userId\"],\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m...\u001b[0m \r\n\u001b[7m148\u001b[0m       name: \"FK_SESSION_USER\",\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m149\u001b[0m     });\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/database/migrations/1754923810374-CreateUserAndSessionTables.ts\u001b[0m:\u001b[93m143\u001b[0m:\u001b[93m52\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ columnNames: string[]; referencedColumnNames: string[]; referencedTableName: string; onDelete: string; name: string; }' is not assignable to parameter of type 'TableForeignKey'.\r\n  Type '{ columnNames: string[]; referencedColumnNames: string[]; referencedTableName: string; onDelete: string; name: string; }' is missing the following properties from type 'TableForeignKey': \"@instanceof\", clone\r\n\r\n\u001b[7m143\u001b[0m     await queryRunner.createForeignKey(\"sessions\", {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                   ~\u001b[0m\r\n\u001b[7m144\u001b[0m       columnNames: [\"userId\"],\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m...\u001b[0m \r\n\u001b[7m148\u001b[0m       name: \"FK_SESSION_USER\",\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m149\u001b[0m     });\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2345],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/database/migrations/1754923810374-CreateUserAndSessionTables.ts\u001b[0m:\u001b[93m143\u001b[0m:\u001b[93m52\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ columnNames: string[]; referencedColumnNames: string[]; referencedTableName: string; onDelete: string; name: string; }' is not assignable to parameter of type 'TableForeignKey'.\r\n  Type '{ columnNames: string[]; referencedColumnNames: string[]; referencedTableName: string; onDelete: string; name: string; }' is missing the following properties from type 'TableForeignKey': \"@instanceof\", clone\r\n\r\n\u001b[7m143\u001b[0m     await queryRunner.createForeignKey(\"sessions\", {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                   ~\u001b[0m\r\n\u001b[7m144\u001b[0m       columnNames: [\"userId\"],\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m...\u001b[0m \r\n\u001b[7m148\u001b[0m       name: \"FK_SESSION_USER\",\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m149\u001b[0m     });\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/database/migrations/1754923810374-CreateUserAndSessionTables.ts\u001b[0m:\u001b[93m143\u001b[0m:\u001b[93m52\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ columnNames: string[]; referencedColumnNames: string[]; referencedTableName: string; onDelete: string; name: string; }' is not assignable to parameter of type 'TableForeignKey'.\r\n  Type '{ columnNames: string[]; referencedColumnNames: string[]; referencedTableName: string; onDelete: string; name: string; }' is missing the following properties from type 'TableForeignKey': \"@instanceof\", clone\r\n\r\n\u001b[7m143\u001b[0m     await queryRunner.createForeignKey(\"sessions\", {\r\n\u001b[7m   \u001b[0m \u001b[91m                                                   ~\u001b[0m\r\n\u001b[7m144\u001b[0m       columnNames: [\"userId\"],\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m...\u001b[0m \r\n\u001b[7m148\u001b[0m       name: \"FK_SESSION_USER\",\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[7m149\u001b[0m     });\r\n\u001b[7m   \u001b[0m \u001b[91m~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2769],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m193\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2769: \u001b[0mNo overload matches this call.\r\n  Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: (SignOptions & { algorithm: \"none\"; }) | undefined): string', gave the following error.\r\n    Argument of type 'string' is not assignable to parameter of type 'null'.\r\n  Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, options?: SignOptions | undefined): string', gave the following error.\r\n    Type 'string' is not assignable to type 'number | StringValue | undefined'.\r\n  Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, callback: SignCallback): void', gave the following error.\r\n    Object literal may only specify known properties, and 'expiresIn' does not exist in type 'SignCallback'.\r\n\r\n\u001b[7m193\u001b[0m     return jwt.sign({ userId }, env.jwt.secret, {\r\n\u001b[7m   \u001b[0m \u001b[91m               ~~~~\u001b[0m\r\n\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m193\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2769: \u001b[0mNo overload matches this call.\r\n  Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: (SignOptions & { algorithm: \"none\"; }) | undefined): string', gave the following error.\r\n    Argument of type 'string' is not assignable to parameter of type 'null'.\r\n  Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, options?: SignOptions | undefined): string', gave the following error.\r\n    Type 'string' is not assignable to type 'number | StringValue | undefined'.\r\n  Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, callback: SignCallback): void', gave the following error.\r\n    Object literal may only specify known properties, and 'expiresIn' does not exist in type 'SignCallback'.\r\n\r\n\u001b[7m193\u001b[0m     return jwt.sign({ userId }, env.jwt.secret, {\r\n\u001b[7m   \u001b[0m \u001b[91m               ~~~~\u001b[0m\r\n\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2769],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m193\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2769: \u001b[0mNo overload matches this call.\r\n  Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: (SignOptions & { algorithm: \"none\"; }) | undefined): string', gave the following error.\r\n    Argument of type 'string' is not assignable to parameter of type 'null'.\r\n  Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, options?: SignOptions | undefined): string', gave the following error.\r\n    Type 'string' is not assignable to type 'number | StringValue | undefined'.\r\n  Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, callback: SignCallback): void', gave the following error.\r\n    Object literal may only specify known properties, and 'expiresIn' does not exist in type 'SignCallback'.\r\n\r\n\u001b[7m193\u001b[0m     return jwt.sign({ userId }, env.jwt.secret as string, {\r\n\u001b[7m   \u001b[0m \u001b[91m               ~~~~\u001b[0m\r\n\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m193\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2769: \u001b[0mNo overload matches this call.\r\n  Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: (SignOptions & { algorithm: \"none\"; }) | undefined): string', gave the following error.\r\n    Argument of type 'string' is not assignable to parameter of type 'null'.\r\n  Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, options?: SignOptions | undefined): string', gave the following error.\r\n    Type 'string' is not assignable to type 'number | StringValue | undefined'.\r\n  Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, callback: SignCallback): void', gave the following error.\r\n    Object literal may only specify known properties, and 'expiresIn' does not exist in type 'SignCallback'.\r\n\r\n\u001b[7m193\u001b[0m     return jwt.sign({ userId }, env.jwt.secret as string, {\r\n\u001b[7m   \u001b[0m \u001b[91m               ~~~~\u001b[0m\r\n\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2769],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m197\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2769: \u001b[0mNo overload matches this call.\r\n  Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: (SignOptions & { algorithm: \"none\"; }) | undefined): string', gave the following error.\r\n    Argument of type 'string' is not assignable to parameter of type 'null'.\r\n  Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, options?: SignOptions | undefined): string', gave the following error.\r\n    Argument of type '{ expiresIn: string; }' is not assignable to parameter of type 'SignOptions'.\r\n      Types of property 'expiresIn' are incompatible.\r\n        Type 'string' is not assignable to type 'number | StringValue | undefined'.\r\n  Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, callback: SignCallback): void', gave the following error.\r\n    Argument of type '{ expiresIn: string; }' is not assignable to parameter of type 'SignCallback'.\r\n      Type '{ expiresIn: string; }' provides no match for the signature '(error: Error | null, encoded?: string | undefined): void'.\r\n\r\n\u001b[7m197\u001b[0m     return jwt.sign(payload, secret, options);\r\n\u001b[7m   \u001b[0m \u001b[91m               ~~~~\u001b[0m\r\n\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m197\u001b[0m:\u001b[93m16\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2769: \u001b[0mNo overload matches this call.\r\n  Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: (SignOptions & { algorithm: \"none\"; }) | undefined): string', gave the following error.\r\n    Argument of type 'string' is not assignable to parameter of type 'null'.\r\n  Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, options?: SignOptions | undefined): string', gave the following error.\r\n    Argument of type '{ expiresIn: string; }' is not assignable to parameter of type 'SignOptions'.\r\n      Types of property 'expiresIn' are incompatible.\r\n        Type 'string' is not assignable to type 'number | StringValue | undefined'.\r\n  Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Secret | Buffer<ArrayBufferLike> | JsonWebKeyInput | PrivateKeyInput, callback: SignCallback): void', gave the following error.\r\n    Argument of type '{ expiresIn: string; }' is not assignable to parameter of type 'SignCallback'.\r\n      Type '{ expiresIn: string; }' provides no match for the signature '(error: Error | null, encoded?: string | undefined): void'.\r\n\r\n\u001b[7m197\u001b[0m     return jwt.sign(payload, secret, options);\r\n\u001b[7m   \u001b[0m \u001b[91m               ~~~~\u001b[0m\r\n\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2322],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m196\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType 'string | number' is not assignable to type 'number | StringValue | undefined'.\r\n  Type 'string' is not assignable to type 'number | StringValue | undefined'.\r\n\r\n\u001b[7m196\u001b[0m       expiresIn: env.jwt.expiresIn as string | number,\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96mnode_modules/@types/jsonwebtoken/index.d.ts\u001b[0m:\u001b[93m43\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m43\u001b[0m     expiresIn?: StringValue | number;\r\n    \u001b[7m  \u001b[0m \u001b[96m    ~~~~~~~~~\u001b[0m\r\n    The expected type comes from property 'expiresIn' which is declared here on type 'SignOptions'\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m196\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2322: \u001b[0mType 'string | number' is not assignable to type 'number | StringValue | undefined'.\r\n  Type 'string' is not assignable to type 'number | StringValue | undefined'.\r\n\r\n\u001b[7m196\u001b[0m       expiresIn: env.jwt.expiresIn as string | number,\r\n\u001b[7m   \u001b[0m \u001b[91m      ~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96mnode_modules/@types/jsonwebtoken/index.d.ts\u001b[0m:\u001b[93m43\u001b[0m:\u001b[93m5\u001b[0m\r\n    \u001b[7m43\u001b[0m     expiresIn?: StringValue | number;\r\n    \u001b[7m  \u001b[0m \u001b[96m    ~~~~~~~~~\u001b[0m\r\n    The expected type comes from property 'expiresIn' which is declared here on type 'SignOptions'\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"MODULE_NOT_FOUND","level":"error","message":"Error connecting to the database Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts","requireStack":["C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts","C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts"],"service":"user-service","stack":"Error: Cannot find module '@/config/logger'\nRequire stack:\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.routes.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\app.ts\n- C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1048:15)\n    at Function.Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at Function.Module._load (node:internal/modules/cjs/loader:901:27)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)\n    at require (node:internal/modules/helpers:130:18)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\base\\BaseService.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)"}
{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJiZmYyYjQ2Mi02NzlkLTQ5YjMtYjY1My0zZTE0YTAwN2ZlNDIiLCJpYXQiOjE3NTUwMDY3NDEsImV4cCI6MTc1NTYxMTU0MX0.AxRkMKUV3cm69tKOwo6-2H8cvaecnocIBWsGUWfyj0Q) » existe déjà.","driverError":{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJiZmYyYjQ2Mi02NzlkLTQ5YjMtYjY1My0zZTE0YTAwN2ZlNDIiLCJpYXQiOjE3NTUwMDY3NDEsImV4cCI6MTc1NTYxMTU0MX0.AxRkMKUV3cm69tKOwo6-2H8cvaecnocIBWsGUWfyj0Q) » existe déjà.","file":"nbtinsert.c","length":443,"line":"673","name":"error","routine":"_bt_check_unique","schema":"public","severity":"ERREUR","table":"sessions"},"file":"nbtinsert.c","length":443,"level":"error","line":"673","message":"AuthController error: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","parameters":["bff2b462-679d-49b3-b653-3e14a007fe42","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJiZmYyYjQ2Mi02NzlkLTQ5YjMtYjY1My0zZTE0YTAwN2ZlNDIiLCJpYXQiOjE3NTUwMDY3NDEsImV4cCI6MTc1NTYxMTU0MX0.AxRkMKUV3cm69tKOwo6-2H8cvaecnocIBWsGUWfyj0Q","2025-08-19T13:52:21.553Z","::1","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"query":"INSERT INTO \"sessions\"(\"id\", \"userId\", \"token\", \"expiresAt\", \"isActive\", \"ipAddress\", \"userAgent\", \"createdAt\") VALUES (DEFAULT, $1, $2, $3, DEFAULT, $4, $5, DEFAULT) RETURNING \"id\", \"isActive\", \"createdAt\"","routine":"_bt_check_unique","schema":"public","service":"user-service","severity":"ERREUR","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:101:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","table":"sessions"}
{"isOperational":true,"level":"error","message":"AuthController error: User with this email already exists","service":"user-service","stack":"Error: User with this email already exists\n    at AuthService.register (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:36:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:24:20","statusCode":409}
{"isOperational":true,"level":"error","message":"AuthController error: User with this email already exists","service":"user-service","stack":"Error: User with this email already exists\n    at AuthService.register (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:36:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:24:20","statusCode":409}
{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ZjdiY2YzZi0yZjQ4LTQyOWEtODI3ZS0zMTlmODRiY2E4OTQiLCJpYXQiOjE3NTUwMDY4NzIsImV4cCI6MTc1NTYxMTY3Mn0.BNjcv7U5nY66Wr8OjyU8BVOzCTRuy1qR-Yh6FdEI680) » existe déjà.","driverError":{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ZjdiY2YzZi0yZjQ4LTQyOWEtODI3ZS0zMTlmODRiY2E4OTQiLCJpYXQiOjE3NTUwMDY4NzIsImV4cCI6MTc1NTYxMTY3Mn0.BNjcv7U5nY66Wr8OjyU8BVOzCTRuy1qR-Yh6FdEI680) » existe déjà.","file":"nbtinsert.c","length":443,"line":"673","name":"error","routine":"_bt_check_unique","schema":"public","severity":"ERREUR","table":"sessions"},"file":"nbtinsert.c","length":443,"level":"error","line":"673","message":"AuthController error: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","parameters":["6f7bcf3f-2f48-429a-827e-319f84bca894","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ZjdiY2YzZi0yZjQ4LTQyOWEtODI3ZS0zMTlmODRiY2E4OTQiLCJpYXQiOjE3NTUwMDY4NzIsImV4cCI6MTc1NTYxMTY3Mn0.BNjcv7U5nY66Wr8OjyU8BVOzCTRuy1qR-Yh6FdEI680","2025-08-19T13:54:32.677Z","::1","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"query":"INSERT INTO \"sessions\"(\"id\", \"userId\", \"token\", \"expiresAt\", \"isActive\", \"ipAddress\", \"userAgent\", \"createdAt\") VALUES (DEFAULT, $1, $2, $3, DEFAULT, $4, $5, DEFAULT) RETURNING \"id\", \"isActive\", \"createdAt\"","routine":"_bt_check_unique","schema":"public","service":"user-service","severity":"ERREUR","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:101:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","table":"sessions"}
{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5NTE0MTAxOC00YjYyLTQ0YjQtOTUxYi0yNmI0OWFiOTVjMjIiLCJpYXQiOjE3NTUwMDY5NTksImV4cCI6MTc1NTYxMTc1OX0.5RfpW4WH9sS6qd-Am7_6fhqOo9BYQ5NtDTalzlykl0E) » existe déjà.","driverError":{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5NTE0MTAxOC00YjYyLTQ0YjQtOTUxYi0yNmI0OWFiOTVjMjIiLCJpYXQiOjE3NTUwMDY5NTksImV4cCI6MTc1NTYxMTc1OX0.5RfpW4WH9sS6qd-Am7_6fhqOo9BYQ5NtDTalzlykl0E) » existe déjà.","file":"nbtinsert.c","length":443,"line":"673","name":"error","routine":"_bt_check_unique","schema":"public","severity":"ERREUR","table":"sessions"},"file":"nbtinsert.c","length":443,"level":"error","line":"673","message":"AuthController error: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","parameters":["95141018-4b62-44b4-951b-26b49ab95c22","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5NTE0MTAxOC00YjYyLTQ0YjQtOTUxYi0yNmI0OWFiOTVjMjIiLCJpYXQiOjE3NTUwMDY5NTksImV4cCI6MTc1NTYxMTc1OX0.5RfpW4WH9sS6qd-Am7_6fhqOo9BYQ5NtDTalzlykl0E","2025-08-19T13:55:59.519Z","::1","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"query":"INSERT INTO \"sessions\"(\"id\", \"userId\", \"token\", \"expiresAt\", \"isActive\", \"ipAddress\", \"userAgent\", \"createdAt\") VALUES (DEFAULT, $1, $2, $3, DEFAULT, $4, $5, DEFAULT) RETURNING \"id\", \"isActive\", \"createdAt\"","routine":"_bt_check_unique","schema":"public","service":"user-service","severity":"ERREUR","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:101:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","table":"sessions"}
{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2OTZjMTE3Zi1iYTM4LTRlNDgtOTU5MC04YzNhM2RjNDg0MWQiLCJpYXQiOjE3NTUwMDcwMjgsImV4cCI6MTc1NTYxMTgyOH0.LYkdb1pZzHyzCRAquTEHEH4RCxqi-jeJ_iML9hPbPLo) » existe déjà.","driverError":{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2OTZjMTE3Zi1iYTM4LTRlNDgtOTU5MC04YzNhM2RjNDg0MWQiLCJpYXQiOjE3NTUwMDcwMjgsImV4cCI6MTc1NTYxMTgyOH0.LYkdb1pZzHyzCRAquTEHEH4RCxqi-jeJ_iML9hPbPLo) » existe déjà.","file":"nbtinsert.c","length":443,"line":"673","name":"error","routine":"_bt_check_unique","schema":"public","severity":"ERREUR","table":"sessions"},"file":"nbtinsert.c","length":443,"level":"error","line":"673","message":"AuthController error: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","parameters":["696c117f-ba38-4e48-9590-8c3a3dc4841d","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2OTZjMTE3Zi1iYTM4LTRlNDgtOTU5MC04YzNhM2RjNDg0MWQiLCJpYXQiOjE3NTUwMDcwMjgsImV4cCI6MTc1NTYxMTgyOH0.LYkdb1pZzHyzCRAquTEHEH4RCxqi-jeJ_iML9hPbPLo","2025-08-19T13:57:08.408Z","::1","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"query":"INSERT INTO \"sessions\"(\"id\", \"userId\", \"token\", \"expiresAt\", \"isActive\", \"ipAddress\", \"userAgent\", \"createdAt\") VALUES (DEFAULT, $1, $2, $3, DEFAULT, $4, $5, DEFAULT) RETURNING \"id\", \"isActive\", \"createdAt\"","routine":"_bt_check_unique","schema":"public","service":"user-service","severity":"ERREUR","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:101:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","table":"sessions"}
{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI1NWNlY2E2Mi1iMDY5LTQ5YTUtYTIzZC01Nzc1NGVjNTdkOTIiLCJpYXQiOjE3NTUwMDcwOTQsImV4cCI6MTc1NTYxMTg5NH0.XtL-05rAUQJDpEDEFCGlUyZBDUEiL6vqHwZxtMkpfmg) » existe déjà.","driverError":{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI1NWNlY2E2Mi1iMDY5LTQ5YTUtYTIzZC01Nzc1NGVjNTdkOTIiLCJpYXQiOjE3NTUwMDcwOTQsImV4cCI6MTc1NTYxMTg5NH0.XtL-05rAUQJDpEDEFCGlUyZBDUEiL6vqHwZxtMkpfmg) » existe déjà.","file":"nbtinsert.c","length":443,"line":"673","name":"error","routine":"_bt_check_unique","schema":"public","severity":"ERREUR","table":"sessions"},"file":"nbtinsert.c","length":443,"level":"error","line":"673","message":"AuthController error: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","parameters":["55ceca62-b069-49a5-a23d-57754ec57d92","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI1NWNlY2E2Mi1iMDY5LTQ5YTUtYTIzZC01Nzc1NGVjNTdkOTIiLCJpYXQiOjE3NTUwMDcwOTQsImV4cCI6MTc1NTYxMTg5NH0.XtL-05rAUQJDpEDEFCGlUyZBDUEiL6vqHwZxtMkpfmg","2025-08-19T13:58:14.785Z","::1","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"query":"INSERT INTO \"sessions\"(\"id\", \"userId\", \"token\", \"expiresAt\", \"isActive\", \"ipAddress\", \"userAgent\", \"createdAt\") VALUES (DEFAULT, $1, $2, $3, DEFAULT, $4, $5, DEFAULT) RETURNING \"id\", \"isActive\", \"createdAt\"","routine":"_bt_check_unique","schema":"public","service":"user-service","severity":"ERREUR","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:101:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","table":"sessions"}
{"diagnosticCodes":[2339,7006,2353],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m40\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m     const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                       ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m       async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'token' does not exist in type 'AuthResponseDto'.\r\n\r\n\u001b[7m71\u001b[0m       token: token, // 🔑 The essential token for the client\r\n\u001b[7m  \u001b[0m \u001b[91m      ~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m40\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m     const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                       ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m       async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'token' does not exist in type 'AuthResponseDto'.\r\n\r\n\u001b[7m71\u001b[0m       token: token, // 🔑 The essential token for the client\r\n\u001b[7m  \u001b[0m \u001b[91m      ~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2339,7006,2353],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m38\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m   const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                     ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m12\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m     async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m           ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'token' does not exist in type 'AuthResponseDto'.\r\n\r\n\u001b[7m71\u001b[0m     token: token, // 🔑 The essential token for the client\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m38\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m   const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                     ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m12\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m     async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m           ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'token' does not exist in type 'AuthResponseDto'.\r\n\r\n\u001b[7m71\u001b[0m     token: token, // 🔑 The essential token for the client\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2339,7006,2353],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m38\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m   const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                     ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m12\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m     async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m           ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'token' does not exist in type 'AuthResponseDto'.\r\n\r\n\u001b[7m71\u001b[0m     token: token, // 🔑 The essential token for the client\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m38\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m   const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                     ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m12\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m     async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m           ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'token' does not exist in type 'AuthResponseDto'.\r\n\r\n\u001b[7m71\u001b[0m     token: token, // 🔑 The essential token for the client\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2339,7006,2353],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m38\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m   const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                     ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m12\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m     async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m           ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'token' does not exist in type 'AuthResponseDto'.\r\n\r\n\u001b[7m71\u001b[0m     token: token, // 🔑 The essential token for the client\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m38\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m   const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                     ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m12\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m     async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m           ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m71\u001b[0m:\u001b[93m5\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2353: \u001b[0mObject literal may only specify known properties, and 'token' does not exist in type 'AuthResponseDto'.\r\n\r\n\u001b[7m71\u001b[0m     token: token, // 🔑 The essential token for the client\r\n\u001b[7m  \u001b[0m \u001b[91m    ~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2741],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m62\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2741: \u001b[0mProperty 'token' is missing in type '{ user: UserProfileDto; message: string; }' but required in type 'AuthResponseDto'.\r\n\r\n\u001b[7m62\u001b[0m       return {\r\n\u001b[7m  \u001b[0m \u001b[91m      ~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.dto.ts\u001b[0m:\u001b[93m51\u001b[0m:\u001b[93m3\u001b[0m\r\n    \u001b[7m51\u001b[0m   token: string;\r\n    \u001b[7m  \u001b[0m \u001b[96m  ~~~~~\u001b[0m\r\n    'token' is declared here.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m62\u001b[0m:\u001b[93m7\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2741: \u001b[0mProperty 'token' is missing in type '{ user: UserProfileDto; message: string; }' but required in type 'AuthResponseDto'.\r\n\r\n\u001b[7m62\u001b[0m       return {\r\n\u001b[7m  \u001b[0m \u001b[91m      ~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.dto.ts\u001b[0m:\u001b[93m51\u001b[0m:\u001b[93m3\u001b[0m\r\n    \u001b[7m51\u001b[0m   token: string;\r\n    \u001b[7m  \u001b[0m \u001b[96m  ~~~~~\u001b[0m\r\n    'token' is declared here.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2339,7006],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m40\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m     const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                       ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m       async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m44\u001b[0m:\u001b[93m40\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m44\u001b[0m     const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                       ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m45\u001b[0m       async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2339,2341,2339,7006],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m23\u001b[0m     this.entityManager = this.authRepository.userRepository.manager;\r\n\u001b[7m  \u001b[0m \u001b[91m         ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m46\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2341: \u001b[0mProperty 'userRepository' is private and only accessible within class 'AuthRepository'.\r\n\r\n\u001b[7m23\u001b[0m     this.entityManager = this.authRepository.userRepository.manager;\r\n\u001b[7m  \u001b[0m \u001b[91m                                             ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m40\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m45\u001b[0m     const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                       ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m46\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m46\u001b[0m       async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m23\u001b[0m     this.entityManager = this.authRepository.userRepository.manager;\r\n\u001b[7m  \u001b[0m \u001b[91m         ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m46\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2341: \u001b[0mProperty 'userRepository' is private and only accessible within class 'AuthRepository'.\r\n\r\n\u001b[7m23\u001b[0m     this.entityManager = this.authRepository.userRepository.manager;\r\n\u001b[7m  \u001b[0m \u001b[91m                                             ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m40\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m45\u001b[0m     const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                       ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m46\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m46\u001b[0m       async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2339,2341,2339,7006],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m23\u001b[0m     this.entityManager = this.authRepository.userRepository.manager;\r\n\u001b[7m  \u001b[0m \u001b[91m         ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m46\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2341: \u001b[0mProperty 'userRepository' is private and only accessible within class 'AuthRepository'.\r\n\r\n\u001b[7m23\u001b[0m     this.entityManager = this.authRepository.userRepository.manager;\r\n\u001b[7m  \u001b[0m \u001b[91m                                             ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m40\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m45\u001b[0m     const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                       ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m46\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m46\u001b[0m       async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m23\u001b[0m     this.entityManager = this.authRepository.userRepository.manager;\r\n\u001b[7m  \u001b[0m \u001b[91m         ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m23\u001b[0m:\u001b[93m46\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2341: \u001b[0mProperty 'userRepository' is private and only accessible within class 'AuthRepository'.\r\n\r\n\u001b[7m23\u001b[0m     this.entityManager = this.authRepository.userRepository.manager;\r\n\u001b[7m  \u001b[0m \u001b[91m                                             ~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m45\u001b[0m:\u001b[93m40\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'entityManager' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m45\u001b[0m     const { user, token } = await this.entityManager.transaction(\r\n\u001b[7m  \u001b[0m \u001b[91m                                       ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m46\u001b[0m:\u001b[93m14\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS7006: \u001b[0mParameter 'transactionalEntityManager' implicitly has an 'any' type.\r\n\r\n\u001b[7m46\u001b[0m       async (transactionalEntityManager) => {\r\n\u001b[7m  \u001b[0m \u001b[91m             ~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2339,2339,2339,2339,2339],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m24\u001b[0m:\u001b[93m43\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'register' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m24\u001b[0m     const result = await this.authService.register(\r\n\u001b[7m  \u001b[0m \u001b[91m                                          ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m31\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'login' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m31\u001b[0m     const token = await this.authService.login(\r\n\u001b[7m  \u001b[0m \u001b[91m                                         ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m55\u001b[0m:\u001b[93m43\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'login' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m55\u001b[0m     const result = await this.authService.login(\r\n\u001b[7m  \u001b[0m \u001b[91m                                          ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m82\u001b[0m:\u001b[93m30\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'logout' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m82\u001b[0m       await this.authService.logout(token);\r\n\u001b[7m  \u001b[0m \u001b[91m                             ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m107\u001b[0m:\u001b[93m30\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'logoutAllSessions' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m107\u001b[0m       await this.authService.logoutAllSessions(userId);\r\n\u001b[7m   \u001b[0m \u001b[91m                             ~~~~~~~~~~~~~~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m24\u001b[0m:\u001b[93m43\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'register' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m24\u001b[0m     const result = await this.authService.register(\r\n\u001b[7m  \u001b[0m \u001b[91m                                          ~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m31\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'login' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m31\u001b[0m     const token = await this.authService.login(\r\n\u001b[7m  \u001b[0m \u001b[91m                                         ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m55\u001b[0m:\u001b[93m43\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'login' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m55\u001b[0m     const result = await this.authService.login(\r\n\u001b[7m  \u001b[0m \u001b[91m                                          ~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m82\u001b[0m:\u001b[93m30\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'logout' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m82\u001b[0m       await this.authService.logout(token);\r\n\u001b[7m  \u001b[0m \u001b[91m                             ~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m107\u001b[0m:\u001b[93m30\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'logoutAllSessions' does not exist on type 'AuthService'.\r\n\r\n\u001b[7m107\u001b[0m       await this.authService.logoutAllSessions(userId);\r\n\u001b[7m   \u001b[0m \u001b[91m                             ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m20\u001b[0m   constructor(private readonly entityManager: EntityManager ) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m20\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m20\u001b[0m   constructor(private readonly entityManager: EntityManager ) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2552],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m63\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2552: \u001b[0mCannot find name 'getEntityManager'. Did you mean 'entityManager'?\r\n\r\n\u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager = getEntityManager()) {\r\n\u001b[7m  \u001b[0m \u001b[91m                                                              ~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager = getEntityManager()) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    'entityManager' is declared here.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m63\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2552: \u001b[0mCannot find name 'getEntityManager'. Did you mean 'entityManager'?\r\n\r\n\u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager = getEntityManager()) {\r\n\u001b[7m  \u001b[0m \u001b[91m                                                              ~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager = getEntityManager()) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    'entityManager' is declared here.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m63\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1-2 arguments, but got 0.\r\n\r\n\u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager = new EntityManager()) {\r\n\u001b[7m  \u001b[0m \u001b[91m                                                              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96mnode_modules/typeorm/entity-manager/EntityManager.d.ts\u001b[0m:\u001b[93m53\u001b[0m:\u001b[93m17\u001b[0m\r\n    \u001b[7m53\u001b[0m     constructor(connection: DataSource, queryRunner?: QueryRunner);\r\n    \u001b[7m  \u001b[0m \u001b[96m                ~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'connection' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m63\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1-2 arguments, but got 0.\r\n\r\n\u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager = new EntityManager()) {\r\n\u001b[7m  \u001b[0m \u001b[91m                                                              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96mnode_modules/typeorm/entity-manager/EntityManager.d.ts\u001b[0m:\u001b[93m53\u001b[0m:\u001b[93m17\u001b[0m\r\n    \u001b[7m53\u001b[0m     constructor(connection: DataSource, queryRunner?: QueryRunner);\r\n    \u001b[7m  \u001b[0m \u001b[96m                ~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'connection' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m65\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1-2 arguments, but got 0.\r\n\r\n\u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager =   new EntityManager() ) {\r\n\u001b[7m  \u001b[0m \u001b[91m                                                                ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96mnode_modules/typeorm/entity-manager/EntityManager.d.ts\u001b[0m:\u001b[93m53\u001b[0m:\u001b[93m17\u001b[0m\r\n    \u001b[7m53\u001b[0m     constructor(connection: DataSource, queryRunner?: QueryRunner);\r\n    \u001b[7m  \u001b[0m \u001b[96m                ~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'connection' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m65\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1-2 arguments, but got 0.\r\n\r\n\u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager =   new EntityManager() ) {\r\n\u001b[7m  \u001b[0m \u001b[91m                                                                ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96mnode_modules/typeorm/entity-manager/EntityManager.d.ts\u001b[0m:\u001b[93m53\u001b[0m:\u001b[93m17\u001b[0m\r\n    \u001b[7m53\u001b[0m     constructor(connection: DataSource, queryRunner?: QueryRunner);\r\n    \u001b[7m  \u001b[0m \u001b[96m                ~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'connection' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService( );\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService( );\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m7\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m7\u001b[0m const authController = new AuthController();\r\n\u001b[7m \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m7\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m7\u001b[0m const authController = new AuthController();\r\n\u001b[7m \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m7\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m7\u001b[0m const authController = new AuthController( );\r\n\u001b[7m \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m7\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m7\u001b[0m const authController = new AuthController( );\r\n\u001b[7m \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2304,2552,2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m8\u001b[0m:\u001b[93m23\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'AppDataSource'.\r\n\r\n\u001b[7m8\u001b[0m const entityManager = AppDataSource.manager;\r\n\u001b[7m \u001b[0m \u001b[91m                      ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m11\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2552: \u001b[0mCannot find name 'AuthService'. Did you mean 'authService'?\r\n\r\n\u001b[7m11\u001b[0m const authService = new AuthService(entityManager);\r\n\u001b[7m  \u001b[0m \u001b[91m                        ~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m11\u001b[0m:\u001b[93m7\u001b[0m\r\n    \u001b[7m11\u001b[0m const authService = new AuthService(entityManager);\r\n    \u001b[7m  \u001b[0m \u001b[96m      ~~~~~~~~~~~\u001b[0m\r\n    'authService' is declared here.\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m const authController = new AuthController();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m8\u001b[0m:\u001b[93m23\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'AppDataSource'.\r\n\r\n\u001b[7m8\u001b[0m const entityManager = AppDataSource.manager;\r\n\u001b[7m \u001b[0m \u001b[91m                      ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m11\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2552: \u001b[0mCannot find name 'AuthService'. Did you mean 'authService'?\r\n\r\n\u001b[7m11\u001b[0m const authService = new AuthService(entityManager);\r\n\u001b[7m  \u001b[0m \u001b[91m                        ~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m11\u001b[0m:\u001b[93m7\u001b[0m\r\n    \u001b[7m11\u001b[0m const authService = new AuthService(entityManager);\r\n    \u001b[7m  \u001b[0m \u001b[96m      ~~~~~~~~~~~\u001b[0m\r\n    'authService' is declared here.\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m const authController = new AuthController();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2305,2552,2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m5\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"../../database/connection\"' has no exported member 'AppDataSource'.\r\n\r\n\u001b[7m5\u001b[0m import { AppDataSource } from \"../../database/connection\";\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m12\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2552: \u001b[0mCannot find name 'AuthService'. Did you mean 'authService'?\r\n\r\n\u001b[7m12\u001b[0m const authService = new AuthService(entityManager);\r\n\u001b[7m  \u001b[0m \u001b[91m                        ~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m12\u001b[0m:\u001b[93m7\u001b[0m\r\n    \u001b[7m12\u001b[0m const authService = new AuthService(entityManager);\r\n    \u001b[7m  \u001b[0m \u001b[96m      ~~~~~~~~~~~\u001b[0m\r\n    'authService' is declared here.\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m14\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m14\u001b[0m const authController = new AuthController();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m5\u001b[0m:\u001b[93m10\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2305: \u001b[0mModule '\"../../database/connection\"' has no exported member 'AppDataSource'.\r\n\r\n\u001b[7m5\u001b[0m import { AppDataSource } from \"../../database/connection\";\r\n\u001b[7m \u001b[0m \u001b[91m         ~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m12\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2552: \u001b[0mCannot find name 'AuthService'. Did you mean 'authService'?\r\n\r\n\u001b[7m12\u001b[0m const authService = new AuthService(entityManager);\r\n\u001b[7m  \u001b[0m \u001b[91m                        ~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m12\u001b[0m:\u001b[93m7\u001b[0m\r\n    \u001b[7m12\u001b[0m const authService = new AuthService(entityManager);\r\n    \u001b[7m  \u001b[0m \u001b[96m      ~~~~~~~~~~~\u001b[0m\r\n    'authService' is declared here.\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m14\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m14\u001b[0m const authController = new AuthController();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2552,2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m12\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2552: \u001b[0mCannot find name 'AuthService'. Did you mean 'authService'?\r\n\r\n\u001b[7m12\u001b[0m const authService = new AuthService(entityManager);\r\n\u001b[7m  \u001b[0m \u001b[91m                        ~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m12\u001b[0m:\u001b[93m7\u001b[0m\r\n    \u001b[7m12\u001b[0m const authService = new AuthService(entityManager);\r\n    \u001b[7m  \u001b[0m \u001b[96m      ~~~~~~~~~~~\u001b[0m\r\n    'authService' is declared here.\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m14\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m14\u001b[0m const authController = new AuthController();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m12\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2552: \u001b[0mCannot find name 'AuthService'. Did you mean 'authService'?\r\n\r\n\u001b[7m12\u001b[0m const authService = new AuthService(entityManager);\r\n\u001b[7m  \u001b[0m \u001b[91m                        ~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m12\u001b[0m:\u001b[93m7\u001b[0m\r\n    \u001b[7m12\u001b[0m const authService = new AuthService(entityManager);\r\n    \u001b[7m  \u001b[0m \u001b[96m      ~~~~~~~~~~~\u001b[0m\r\n    'authService' is declared here.\r\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m14\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m14\u001b[0m const authController = new AuthController();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m15\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m15\u001b[0m const authController = new AuthController();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m15\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m15\u001b[0m const authController = new AuthController();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.middleware.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m35\u001b[0m   private static authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                               ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.middleware.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m35\u001b[0m   private static authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                               ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.middleware.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m35\u001b[0m   private static authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                               ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.middleware.ts\u001b[0m:\u001b[93m35\u001b[0m:\u001b[93m32\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m35\u001b[0m   private static authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                               ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m8\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m8\u001b[0m const authController = new AuthController( );\r\n\u001b[7m \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m8\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m8\u001b[0m const authController = new AuthController( );\r\n\u001b[7m \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m8\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m8\u001b[0m const authController = new AuthController( );\r\n\u001b[7m \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.routes.ts\u001b[0m:\u001b[93m8\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m8\u001b[0m const authController = new AuthController( );\r\n\u001b[7m \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m10\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m10\u001b[0m   constructor(private readonly authService: AuthService) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'authService' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"diagnosticCodes":[2554],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/auth/auth.controller.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2554: \u001b[0mExpected 1 arguments, but got 0.\r\n\r\n\u001b[7m13\u001b[0m     this.authService = new AuthService();\r\n\u001b[7m  \u001b[0m \u001b[91m                       ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/features/auth/auth.service.ts\u001b[0m:\u001b[93m21\u001b[0m:\u001b[93m15\u001b[0m\r\n    \u001b[7m21\u001b[0m   constructor(private readonly entityManager: EntityManager) {\r\n    \u001b[7m  \u001b[0m \u001b[96m              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n    An argument for 'entityManager' was not provided.\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJiNDMyM2EzYi0zYzNjLTRlMGQtYTI5MC01NzkxNmQ4NTc2OTkiLCJpYXQiOjE3NTUwMDgwOTIsImV4cCI6MTc1NTYxMjg5Mn0.KBLM-mRestG11QLKjyEZk_XsDOLuOfPdWhISCQv2YZ0) » existe déjà.","driverError":{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJiNDMyM2EzYi0zYzNjLTRlMGQtYTI5MC01NzkxNmQ4NTc2OTkiLCJpYXQiOjE3NTUwMDgwOTIsImV4cCI6MTc1NTYxMjg5Mn0.KBLM-mRestG11QLKjyEZk_XsDOLuOfPdWhISCQv2YZ0) » existe déjà.","file":"nbtinsert.c","length":443,"line":"673","name":"error","routine":"_bt_check_unique","schema":"public","severity":"ERREUR","table":"sessions"},"file":"nbtinsert.c","length":443,"level":"error","line":"673","message":"AuthController error: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","parameters":["b4323a3b-3c3c-4e0d-a290-57916d857699","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJiNDMyM2EzYi0zYzNjLTRlMGQtYTI5MC01NzkxNmQ4NTc2OTkiLCJpYXQiOjE3NTUwMDgwOTIsImV4cCI6MTc1NTYxMjg5Mn0.KBLM-mRestG11QLKjyEZk_XsDOLuOfPdWhISCQv2YZ0","2025-08-19T14:14:52.447Z","::1","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"query":"INSERT INTO \"sessions\"(\"id\", \"userId\", \"token\", \"expiresAt\", \"isActive\", \"ipAddress\", \"userAgent\", \"createdAt\") VALUES (DEFAULT, $1, $2, $3, DEFAULT, $4, $5, DEFAULT) RETURNING \"id\", \"isActive\", \"createdAt\"","routine":"_bt_check_unique","schema":"public","service":"user-service","severity":"ERREUR","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:102:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","table":"sessions"}
{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI3NDU0MjFlYS00NTk5LTRkNDMtODVhZC05OTRjZDUzOGYzNTEiLCJpYXQiOjE3NTUwMDgzODQsImV4cCI6MTc1NTYxMzE4NH0.UHSWY1DmTIlrslL_4UFaUvUdUdCoycS_7N5_yyxZMGg) » existe déjà.","driverError":{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI3NDU0MjFlYS00NTk5LTRkNDMtODVhZC05OTRjZDUzOGYzNTEiLCJpYXQiOjE3NTUwMDgzODQsImV4cCI6MTc1NTYxMzE4NH0.UHSWY1DmTIlrslL_4UFaUvUdUdCoycS_7N5_yyxZMGg) » existe déjà.","file":"nbtinsert.c","length":443,"line":"673","name":"error","routine":"_bt_check_unique","schema":"public","severity":"ERREUR","table":"sessions"},"file":"nbtinsert.c","length":443,"level":"error","line":"673","message":"AuthController error: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","parameters":["745421ea-4599-4d43-85ad-994cd538f351","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI3NDU0MjFlYS00NTk5LTRkNDMtODVhZC05OTRjZDUzOGYzNTEiLCJpYXQiOjE3NTUwMDgzODQsImV4cCI6MTc1NTYxMzE4NH0.UHSWY1DmTIlrslL_4UFaUvUdUdCoycS_7N5_yyxZMGg","2025-08-19T14:19:44.818Z","::1","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"query":"INSERT INTO \"sessions\"(\"id\", \"userId\", \"token\", \"expiresAt\", \"isActive\", \"ipAddress\", \"userAgent\", \"createdAt\") VALUES (DEFAULT, $1, $2, $3, DEFAULT, $4, $5, DEFAULT) RETURNING \"id\", \"isActive\", \"createdAt\"","routine":"_bt_check_unique","schema":"public","service":"user-service","severity":"ERREUR","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:102:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","table":"sessions"}
{"error":{"message":"la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","name":"QueryFailedError","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:102:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","statusCode":500},"ip":"::1","level":"error","message":"Server Error","method":"POST","requestId":"req_1755008384841_9bqkxp1rd","service":"user-service","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5M2E4NGRjYy1iZTk3LTQ0NzktYTIxYy04NmJiNGM1ZTdhNDYiLCJpYXQiOjE3NTUwMDg0MjMsImV4cCI6MTc1NTYxMzIyM30.K1Grp_Nyz-UM8bFezVM_NbT5yditgWc7gIHHCbilelI) » existe déjà.","driverError":{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5M2E4NGRjYy1iZTk3LTQ0NzktYTIxYy04NmJiNGM1ZTdhNDYiLCJpYXQiOjE3NTUwMDg0MjMsImV4cCI6MTc1NTYxMzIyM30.K1Grp_Nyz-UM8bFezVM_NbT5yditgWc7gIHHCbilelI) » existe déjà.","file":"nbtinsert.c","length":443,"line":"673","name":"error","routine":"_bt_check_unique","schema":"public","severity":"ERREUR","table":"sessions"},"file":"nbtinsert.c","length":443,"level":"error","line":"673","message":"AuthController error: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","parameters":["93a84dcc-be97-4479-a21c-86bb4c5e7a46","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI5M2E4NGRjYy1iZTk3LTQ0NzktYTIxYy04NmJiNGM1ZTdhNDYiLCJpYXQiOjE3NTUwMDg0MjMsImV4cCI6MTc1NTYxMzIyM30.K1Grp_Nyz-UM8bFezVM_NbT5yditgWc7gIHHCbilelI","2025-08-19T14:20:23.873Z","::1","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"query":"INSERT INTO \"sessions\"(\"id\", \"userId\", \"token\", \"expiresAt\", \"isActive\", \"ipAddress\", \"userAgent\", \"createdAt\") VALUES (DEFAULT, $1, $2, $3, DEFAULT, $4, $5, DEFAULT) RETURNING \"id\", \"isActive\", \"createdAt\"","routine":"_bt_check_unique","schema":"public","service":"user-service","severity":"ERREUR","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:102:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","table":"sessions"}
{"error":{"message":"la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","name":"QueryFailedError","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:102:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","statusCode":500},"ip":"::1","level":"error","message":"Server Error","method":"POST","requestId":"req_1755008423876_vjpxg3s20","service":"user-service","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIyYjVjMTM5Ni00ODRhLTQ2YjAtYmMxOC1kMTBjMDZiMTViYTYiLCJpYXQiOjE3NTUwMDg0NTgsImV4cCI6MTc1NTYxMzI1OH0.AyBDWODiAJq9i3QZu8zpySxJoRYz9lagQBAXE0DH8E4) » existe déjà.","driverError":{"code":"23505","constraint":"UQ_e9f62f5dcb8a54b84234c9e7a06","detail":"La clé « (token)=(eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIyYjVjMTM5Ni00ODRhLTQ2YjAtYmMxOC1kMTBjMDZiMTViYTYiLCJpYXQiOjE3NTUwMDg0NTgsImV4cCI6MTc1NTYxMzI1OH0.AyBDWODiAJq9i3QZu8zpySxJoRYz9lagQBAXE0DH8E4) » existe déjà.","file":"nbtinsert.c","length":443,"line":"673","name":"error","routine":"_bt_check_unique","schema":"public","severity":"ERREUR","table":"sessions"},"file":"nbtinsert.c","length":443,"level":"error","line":"673","message":"AuthController error: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","parameters":["2b5c1396-484a-46b0-bc18-d10c06b15ba6","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiIyYjVjMTM5Ni00ODRhLTQ2YjAtYmMxOC1kMTBjMDZiMTViYTYiLCJpYXQiOjE3NTUwMDg0NTgsImV4cCI6MTc1NTYxMzI1OH0.AyBDWODiAJq9i3QZu8zpySxJoRYz9lagQBAXE0DH8E4","2025-08-19T14:20:58.677Z","::1","Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"],"query":"INSERT INTO \"sessions\"(\"id\", \"userId\", \"token\", \"expiresAt\", \"isActive\", \"ipAddress\", \"userAgent\", \"createdAt\") VALUES (DEFAULT, $1, $2, $3, DEFAULT, $4, $5, DEFAULT) RETURNING \"id\", \"isActive\", \"createdAt\"","routine":"_bt_check_unique","schema":"public","service":"user-service","severity":"ERREUR","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:102:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","table":"sessions"}
{"error":{"message":"la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »","name":"QueryFailedError","stack":"QueryFailedError: la valeur d'une clé dupliquée rompt la contrainte unique « UQ_e9f62f5dcb8a54b84234c9e7a06 »\n    at PostgresQueryRunner.query (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\typeorm\\src\\driver\\postgres\\PostgresQueryRunner.ts:325:19)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InsertQueryBuilder.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\query-builder\\InsertQueryBuilder.ts:164:33)\n    at async SubjectExecutor.executeInsertOperations (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:435:42)\n    at async SubjectExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\SubjectExecutor.ts:137:9)\n    at async EntityPersistExecutor.execute (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\src\\persistence\\EntityPersistExecutor.ts:182:21)\n    at async AuthRepository.createSession (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.respository.ts:41:12)\n    at async AuthService.login (C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.service.ts:102:7)\n    at async C:\\Users\\<USER>\\Desktop\\Test project\\server\\src\\features\\auth\\auth.controller.ts:31:19","statusCode":500},"ip":"::1","level":"error","message":"Server Error","method":"POST","requestId":"req_1755008458681_pmghlk4lx","service":"user-service","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"diagnosticCodes":[2345],"level":"error","message":"Error connecting to the database ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m63\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type 'null' is not assignable to parameter of type 'CountryCode | undefined'.\r\n\r\n\u001b[7m63\u001b[0m   @IsPhoneNumber(null, { message: \"Please provide a valid phone number\" })\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~\u001b[0m\r\n","service":"user-service","stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/features/user/user.model.ts\u001b[0m:\u001b[93m63\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type 'null' is not assignable to parameter of type 'CountryCode | undefined'.\r\n\r\n\u001b[7m63\u001b[0m   @IsPhoneNumber(null, { message: \"Please provide a valid phone number\" })\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1295:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Desktop\\Test project\\server\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1091:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:938:12)\n    at Module.require (node:internal/modules/cjs/loader:1115:19)"}
