import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import {
  IsNotEmpty,
  IsNumber,
  Min,
  IsOptional,
  IsString,
  IsUUID,
} from "class-validator";
import { Order } from "./order.model";
import { Product } from "../product/product.model";

export interface ItemCustomization {
  [category: string]: {
    [itemName: string]: number;
  };
}

@Entity("order_items")
export class OrderItem {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "int" })
  @IsNotEmpty({ message: "Quantity is required" })
  @IsNumber({}, { message: "Quantity must be a number" })
  @Min(1, { message: "Quantity must be at least 1" })
  quantity: number;

  @Column({ type: "decimal", precision: 10, scale: 2 })
  @IsNotEmpty({ message: "Unit price is required" })
  @IsNumber({}, { message: "Unit price must be a number" })
  @Min(0, { message: "Unit price must be non-negative" })
  unitPrice: number;

  @Column({ type: "decimal", precision: 10, scale: 2 })
  @IsNotEmpty({ message: "Total price is required" })
  @IsNumber({}, { message: "Total price must be a number" })
  @Min(0, { message: "Total price must be non-negative" })
  totalPrice: number;

  @Column({ type: "json", nullable: true })
  customizations?: ItemCustomization;

  @Column({ type: "text", nullable: true })
  @IsOptional()
  @IsString({ message: "Special instructions must be a string" })
  specialInstructions?: string;

  // Snapshot of product data at time of order
  @Column({ type: "varchar", length: 255 })
  @IsNotEmpty({ message: "Product name is required" })
  @IsString({ message: "Product name must be a string" })
  productName: string;

  @Column({ type: "text", nullable: true })
  @IsOptional()
  @IsString({ message: "Product description must be a string" })
  productDescription?: string;

  @Column({ type: "varchar", length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: "Product image URL must be a string" })
  productImageUrl?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  @IsOptional()
  @IsString({ message: "Category name must be a string" })
  categoryName?: string;

  // Relationships
  @Column({ type: "uuid" })
  @IsUUID(4, { message: "Order ID must be a valid UUID" })
  orderId: string;

  @ManyToOne(() => Order, (order) => order.items, { onDelete: "CASCADE" })
  @JoinColumn({ name: "orderId" })
  order: Order;

  @Column({ type: "uuid" })
  @IsUUID(4, { message: "Product ID must be a valid UUID" })
  productId: string;

  @ManyToOne(() => Product, (product) => product.orderItems)
  @JoinColumn({ name: "productId" })
  product: Product;

  // Timestamps
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  // Virtual properties
  get hasCustomizations(): boolean {
    return this.customizations && Object.keys(this.customizations).length > 0;
  }

  get customizationSummary(): string {
    if (!this.hasCustomizations) return '';
    
    const summary: string[] = [];
    for (const [category, items] of Object.entries(this.customizations!)) {
      for (const [itemName, quantity] of Object.entries(items)) {
        if (quantity > 0) {
          summary.push(`${itemName} (${quantity})`);
        }
      }
    }
    return summary.join(', ');
  }

  // Methods
  calculateTotalPrice(): void {
    this.totalPrice = this.unitPrice * this.quantity;
  }

  static createFromProduct(
    product: Product,
    quantity: number,
    customizations?: ItemCustomization,
    specialInstructions?: string
  ): Partial<OrderItem> {
    const unitPrice = product.price;
    
    // Calculate customization price
    let customizationPrice = 0;
    if (customizations && product.customizationCategories) {
      for (const [categoryName, items] of Object.entries(customizations)) {
        const category = product.customizationCategories.find(c => c.name === categoryName);
        if (category) {
          for (const [itemName, itemQuantity] of Object.entries(items)) {
            const option = category.options.find(o => o.name === itemName);
            if (option && itemQuantity > 0) {
              customizationPrice += option.price * itemQuantity;
            }
          }
        }
      }
    }

    const finalUnitPrice = unitPrice + customizationPrice;
    
    return {
      quantity,
      unitPrice: finalUnitPrice,
      totalPrice: finalUnitPrice * quantity,
      customizations,
      specialInstructions,
      productName: product.name,
      productDescription: product.description,
      productImageUrl: product.imageUrl,
      categoryName: product.category?.name,
      productId: product.id,
    };
  }
}
