import { DataSourceOptions } from "typeorm";
import dotenv from "dotenv";

dotenv.config();

export const databaseConfig: DataSourceOptions = {
  type: "postgres",
  host: process.env.DB_HOST || "localhost",
  port: Number(process.env.DB_PORT) || 5432,
  username: process.env.DB_USERNAME || "postgres",
  password: process.env.DB_PASSWORD || "password",
  database: process.env.DB_NAME || "mydatabase",
  synchronize: process.env.NODE_ENV === "development" ? true : false,
  logging: process.env.NODE_ENV === "development" ? true : false,
  entities: [
    __dirname + "/../**/*.entity{.ts,.js}",
    __dirname + "/../**/*.model{.ts,.js}",
  ],
  migrations: [__dirname + "/../database/migrations/*{.ts,.js}"],
};
