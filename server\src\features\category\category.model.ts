import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm";
import { IsNotEmpty, IsString, IsOptional, IsInt, Min } from "class-validator";
import { Restaurant } from "../restaurant/restaurant.model";
import { Product } from "../product/product.model";

@Entity("categories")
@Index(["name", "restaurantId"], { unique: true })
export class Category {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255 })
  @IsNotEmpty({ message: "Category name is required" })
  @IsString({ message: "Category name must be a string" })
  name: string;

  @Column({ type: "text", nullable: true })
  @IsOptional()
  @IsString({ message: "Description must be a string" })
  description?: string;

  @Column({ type: "varchar", length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: "Image URL must be a string" })
  imageUrl?: string;

  @Column({ type: "int", default: 0 })
  @IsOptional()
  @IsInt({ message: "Sort order must be an integer" })
  @Min(0, { message: "Sort order must be non-negative" })
  sortOrder: number;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @Column({ type: "varchar", length: 7, nullable: true })
  @IsOptional()
  @IsString({ message: "Color must be a string" })
  color?: string; // Hex color code for UI theming

  // Relationships
  @Column({ type: "uuid" })
  restaurantId: string;

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.products)
  @JoinColumn({ name: "restaurantId" })
  restaurant: Restaurant;

  @OneToMany(() => Product, (product) => product.category)
  products: Product[];

  // Timestamps
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  // Virtual properties
  get totalProducts(): number {
    return this.products?.filter(p => !p.deletedAt).length || 0;
  }

  get activeProducts(): Product[] {
    return this.products?.filter(p => p.isAvailable && !p.deletedAt) || [];
  }
}
