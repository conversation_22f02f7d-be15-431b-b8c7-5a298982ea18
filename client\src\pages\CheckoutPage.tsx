import React from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector, useAppDispatch } from "../hooks/redux";
import {
  selectCartItems,
  selectCartTotalPrice,
  addToCart,
  clearCart,
} from "../store/slices/cartSlice";
import { CartItem, Product } from "../features/menu/types/menuTypes";

// Sample recommendation products
const recommendationProducts: Product[] = [
  {
    id: 101,
    name: "fondant au chocolat",
    category: "fondant",
    price: 15,
    image: "https://placehold.co/120x80/8B4513/FFFFFF?text=Fondant",
    description: "Rich chocolate fondant dessert",
  },
  {
    id: 102,
    name: "omelette thon",
    category: "omelette",
    price: 18,
    image: "https://placehold.co/120x80/FFD700/000000?text=Omelette",
    description: "Tuna omelette with fresh ingredients",
  },
];

// Icons
const ArrowLeftIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-6 w-6"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 19l-7-7 7-7"
    />
  </svg>
);

const PlusIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
    />
  </svg>
);

const CheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const cartItems = useAppSelector(selectCartItems);
  const totalPrice = useAppSelector(selectCartTotalPrice);

  const handleAddRecommendation = (product: Product) => {
    dispatch(addToCart({ product, quantity: 1 }));
  };

  const handleViewStatusOrder = () => {
    navigate("/order-status");
  };

  const handleCheckout = () => {
    // Process checkout
    dispatch(clearCart());
    navigate("/order-confirmation");
  };

  return (
    <div className="bg-secondary-50 min-h-screen font-sans">
      <div className="max-w-md mx-auto bg-white shadow-lg min-h-screen">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-white border-b">
          <button
            onClick={() => navigate("/")}
            className="p-2 rounded-full hover:bg-secondary-100 transition-colors"
          >
            <ArrowLeftIcon />
          </button>
          <div className="flex-1 text-center">
            <h1 className="text-lg font-semibold text-secondary-800">
              Commande
            </h1>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-secondary-600">Table Number:</span>
            <span className="bg-primary-500 text-white px-2 py-1 rounded text-sm font-bold">
              1
            </span>
          </div>
        </div>

        {/* Order Items */}
        <div className="p-4">
          {cartItems.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-secondary-500">No items in your order</p>
              <button
                onClick={() => navigate("/")}
                className="mt-4 bg-primary-500 text-white px-6 py-2 rounded-lg hover:bg-primary-600 transition-colors"
              >
                Add Items
              </button>
            </div>
          ) : (
            <>
              {cartItems.map((item: CartItem) => (
                <div
                  key={item.id}
                  className="flex items-center bg-white rounded-lg p-3 mb-3 shadow-sm border"
                >
                  <img
                    src={item.product.image}
                    alt={item.product.name}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                  <div className="flex-1 ml-3">
                    <h3 className="font-semibold text-secondary-800 text-sm">
                      {item.product.name}
                    </h3>
                    <p className="text-xs text-secondary-500 capitalize">
                      {item.product.category}
                    </p>
                    <p className="font-bold text-secondary-800 text-sm">
                      {item.product.price} DT
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="bg-primary-100 rounded-full px-2 py-1">
                      <span className="text-primary-700 font-bold text-sm">
                        {item.quantity}
                      </span>
                    </div>
                  </div>
                </div>
              ))}

              {/* Total */}
              <div className="border-t pt-4 mt-4">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-secondary-800">
                    Total
                  </span>
                  <span className="text-xl font-bold text-secondary-800">
                    {totalPrice.toFixed(3)} DT
                  </span>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Recommendations */}
        {cartItems.length > 0 && (
          <div className="p-4 bg-secondary-50">
            <h2 className="text-lg font-semibold text-secondary-800 mb-4">
              Recommandation
            </h2>
            <div className="grid grid-cols-2 gap-3">
              {recommendationProducts.map((product) => (
                <div
                  key={product.id}
                  className="bg-white rounded-lg p-3 shadow-sm"
                >
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-20 rounded-lg object-cover mb-2"
                  />
                  <h3 className="font-medium text-secondary-800 text-sm mb-1">
                    {product.name}
                  </h3>
                  <p className="text-xs text-secondary-500 capitalize mb-2">
                    {product.category}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="font-bold text-secondary-800 text-sm">
                      {product.price} DT
                    </span>
                    <button
                      onClick={() => handleAddRecommendation(product)}
                      className="bg-primary-500 text-white rounded-full p-1 w-6 h-6 flex items-center justify-center hover:bg-primary-600 transition-colors"
                    >
                      <PlusIcon />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {cartItems.length > 0 && (
          <div className="p-4 space-y-3 bg-white border-t">
            <button
              onClick={handleViewStatusOrder}
              className="w-full bg-secondary-200 text-secondary-800 py-3 rounded-lg font-semibold hover:bg-secondary-300 transition-colors"
            >
              View Status Order
            </button>
            <button
              onClick={handleCheckout}
              className="w-full bg-primary-500 text-white py-3 rounded-lg font-semibold hover:bg-primary-600 transition-colors"
            >
              Checkout
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CheckoutPage;
