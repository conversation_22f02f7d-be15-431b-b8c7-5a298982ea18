import { useAppDispatch, useAppSelector } from "../../../hooks/redux";
import {
  selectSelectedCategory,
  setSelectedCategory,
} from "../../../store/slices/filterSlice";
import { categories } from "../data/menuData";

export const Categories: React.FC = () => {
  const dispatch = useAppDispatch();
  const selectedCategory = useAppSelector(selectSelectedCategory);

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Categories</h2>
      <div className="flex space-x-3 overflow-x-auto pb-2">
        {categories.map((category: any) => (
          <button
            key={category}
            onClick={() => dispatch(setSelectedCategory(category))}
            className={`px-6 py-2 rounded-full text-sm font-semibold whitespace-nowrap ${
              selectedCategory === category
                ? "bg-primary-500 text-white"
                : "bg-secondary-200 text-secondary-700"
            }`}
          >
            {category}
          </button>
        ))}
      </div>
    </div>
  );
};
