import React, { useState } from "react";
import { useNavigate } from "react-router-dom";

// Sample order data - in a real app, this would come from an API or Redux store
const sampleOrder = {
  id: "ORD-001",
  items: [
    {
      id: 1,
      name: "Petit dej Express",
      category: "Brunch",
      image: "https://placehold.co/80x80/FFF4F4/C7A1A1?text=Brunch",
      quantity: 1,
      price: 25,
    },
  ],
  status: "completed" as "preparing" | "ready" | "completed",
  tableNumber: 1,
  orderTime: new Date().toISOString(),
};

// Icons
const ArrowLeftIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-6 w-6"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 19l-7-7 7-7"
    />
  </svg>
);

const CheckCircleIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5 text-green-500"
    fill="currentColor"
    viewBox="0 0 24 24"
  >
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
  </svg>
);

const ClockIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5 text-orange-500"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
    />
  </svg>
);

const OrderStatusPage: React.FC = () => {
  const navigate = useNavigate();
  const [order] = useState(sampleOrder);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircleIcon />;
      case "ready":
        return <CheckCircleIcon />;
      case "preparing":
      default:
        return <ClockIcon />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "completed";
      case "ready":
        return "ready";
      case "preparing":
      default:
        return "preparing";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-600";
      case "ready":
        return "text-green-600";
      case "preparing":
      default:
        return "text-orange-600";
    }
  };

  const handleAddNewOrder = () => {
    navigate("/");
  };

  const handleBackToHome = () => {
    navigate("/");
  };

  return (
    <div className="bg-secondary-50 min-h-screen font-sans">
      <div className="max-w-md mx-auto bg-white shadow-lg min-h-screen">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-white border-b">
          <button
            onClick={handleBackToHome}
            className="p-2 rounded-full hover:bg-secondary-100 transition-colors"
          >
            <ArrowLeftIcon />
          </button>
          <div className="flex-1 text-left ml-4">
            <h1 className="text-lg font-semibold text-secondary-800">Status</h1>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-secondary-600">Table Number:</span>
            <span className="bg-primary-500 text-white px-2 py-1 rounded text-sm font-bold">
              {order.tableNumber}
            </span>
          </div>
        </div>

        {/* Order Items */}
        <div className="p-4">
          {order.items.map((item) => (
            <div
              key={item.id}
              className="bg-white rounded-lg p-4 mb-4 shadow-sm border"
            >
              <div className="flex items-center">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-16 h-16 rounded-lg object-cover"
                />
                <div className="flex-1 ml-4">
                  <h3 className="font-semibold text-secondary-800">
                    {item.name}
                  </h3>
                  <p className="text-sm text-secondary-500 capitalize">
                    {item.category}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(order.status)}
                  <span
                    className={`font-medium text-sm ${getStatusColor(
                      order.status
                    )}`}
                  >
                    {getStatusText(order.status)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Add New Order Button */}
        <div className="p-4">
          <button
            onClick={handleAddNewOrder}
            className="w-full bg-primary-500 text-white py-4 rounded-lg font-semibold hover:bg-primary-600 transition-colors text-lg"
          >
            Add a new order
          </button>
        </div>

        {/* Status Information */}
        <div className="p-4 bg-secondary-50">
          <div className="bg-white rounded-lg p-4">
            <h3 className="font-semibold text-secondary-800 mb-2">
              Order Information
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-secondary-600">Order ID:</span>
                <span className="font-medium text-secondary-800">
                  {order.id}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Table:</span>
                <span className="font-medium text-secondary-800">
                  {order.tableNumber}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Status:</span>
                <span
                  className={`font-medium capitalize ${getStatusColor(
                    order.status
                  )}`}
                >
                  {getStatusText(order.status)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-secondary-600">Order Time:</span>
                <span className="font-medium text-secondary-800">
                  {new Date(order.orderTime).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Status Progress */}
        <div className="p-4">
          <div className="bg-white rounded-lg p-4">
            <h3 className="font-semibold text-secondary-800 mb-4">
              Order Progress
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span className="text-secondary-800">Order Received</span>
              </div>
              <div className="flex items-center space-x-3">
                <div
                  className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    order.status === "preparing" ||
                    order.status === "ready" ||
                    order.status === "completed"
                      ? "bg-green-500"
                      : "bg-secondary-300"
                  }`}
                >
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span className="text-secondary-800">Preparing</span>
              </div>
              <div className="flex items-center space-x-3">
                <div
                  className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    order.status === "ready" || order.status === "completed"
                      ? "bg-green-500"
                      : "bg-secondary-300"
                  }`}
                >
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span className="text-secondary-800">Ready</span>
              </div>
              <div className="flex items-center space-x-3">
                <div
                  className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    order.status === "completed"
                      ? "bg-green-500"
                      : "bg-secondary-300"
                  }`}
                >
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <span className="text-secondary-800">Completed</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderStatusPage;
