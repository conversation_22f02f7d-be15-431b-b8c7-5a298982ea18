import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
} from "typeorm";
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsNumber,
  Min,
  Max,
  IsBoolean,
} from "class-validator";
import { Restaurant } from "../restaurant/restaurant.model";
import { Order } from "../order/order.model";

export enum TableStatus {
  AVAILABLE = 'available',
  OCCUPIED = 'occupied',
  RESERVED = 'reserved',
  OUT_OF_SERVICE = 'out_of_service'
}

@Entity("tables")
@Index(["number", "restaurantId"], { unique: true })
export class Table {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 50 })
  @IsNotEmpty({ message: "Table number is required" })
  @IsString({ message: "Table number must be a string" })
  number: string;

  @Column({ type: "int" })
  @IsNotEmpty({ message: "Capacity is required" })
  @IsNumber({}, { message: "Capacity must be a number" })
  @Min(1, { message: "Capacity must be at least 1" })
  @Max(20, { message: "Capacity cannot exceed 20" })
  capacity: number;

  @Column({
    type: "enum",
    enum: TableStatus,
    default: TableStatus.AVAILABLE,
  })
  status: TableStatus;

  @Column({ type: "varchar", length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: "Location must be a string" })
  location?: string; // e.g., "Window side", "Patio", "Main hall"

  @Column({ type: "varchar", length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: "QR code URL must be a string" })
  qrCodeUrl?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  @IsOptional()
  @IsString({ message: "QR code data must be a string" })
  qrCodeData?: string; // The data encoded in QR code

  @Column({ type: "boolean", default: true })
  @IsOptional()
  @IsBoolean({ message: "isActive must be a boolean" })
  isActive: boolean;

  @Column({ type: "json", nullable: true })
  coordinates?: {
    x: number;
    y: number;
  }; // For restaurant floor plan

  @Column({ type: "text", nullable: true })
  @IsOptional()
  @IsString({ message: "Notes must be a string" })
  notes?: string;

  // Relationships
  @Column({ type: "uuid" })
  restaurantId: string;

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.tables)
  @JoinColumn({ name: "restaurantId" })
  restaurant: Restaurant;

  @OneToMany(() => Order, (order) => order.table)
  orders: Order[];

  // Timestamps
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  // Virtual properties
  get isAvailable(): boolean {
    return this.status === TableStatus.AVAILABLE && this.isActive;
  }

  get currentOrder(): Order | undefined {
    return this.orders?.find(order => 
      order.status !== 'completed' && 
      order.status !== 'cancelled' &&
      !order.deletedAt
    );
  }

  get totalOrders(): number {
    return this.orders?.filter(order => !order.deletedAt).length || 0;
  }

  // Methods
  generateQRCodeData(): string {
    return `${process.env.FRONTEND_URL}/table/${this.id}`;
  }
}
