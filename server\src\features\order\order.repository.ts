import { Repository } from "typeorm";
import { AppDataSource } from "../../database/connection";
import { BaseRepository } from "../../base/BaseRepository";
import { Order, OrderStatus } from "./order.model";
import { OrderItem } from "./order-item.model";
import { OrderQueryDto, OrderStatsDto } from "./order.dto";
import { PaginatedResult } from "../../base/BaseModel";

export class OrderRepository extends BaseRepository<Order> {
  private orderRepository: Repository<Order>;
  private orderItemRepository: Repository<OrderItem>;

  constructor() {
    super(Order);
    this.orderRepository = AppDataSource.getRepository(Order);
    this.orderItemRepository = AppDataSource.getRepository(OrderItem);
  }

  /**
   * Find orders with filtering, searching, and pagination
   */
  async findOrders(
    restaurantId: string,
    query: OrderQueryDto,
    customerId?: string
  ): Promise<PaginatedResult<Order>> {
    const {
      page = 1,
      limit = 10,
      status,
      type,
      customerId: queryCustomerId,
      tableId,
      date,
      fromDate,
      toDate,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = query;

    let queryBuilder = this.orderRepository
      .createQueryBuilder("order")
      .leftJoinAndSelect("order.items", "items")
      .leftJoinAndSelect("items.product", "product")
      .leftJoinAndSelect("order.customer", "customer")
      .leftJoinAndSelect("order.waiter", "waiter")
      .leftJoinAndSelect("order.table", "table")
      .leftJoinAndSelect("order.restaurant", "restaurant")
      .where("order.restaurantId = :restaurantId", { restaurantId })
      .andWhere("order.deletedAt IS NULL");

    // Apply customer filter (for client access)
    if (customerId) {
      queryBuilder = queryBuilder.andWhere("order.customerId = :customerId", {
        customerId,
      });
    }

    // Apply filters
    if (status) {
      queryBuilder = queryBuilder.andWhere("order.status = :status", {
        status,
      });
    }

    if (type) {
      queryBuilder = queryBuilder.andWhere("order.type = :type", { type });
    }

    if (queryCustomerId) {
      queryBuilder = queryBuilder.andWhere(
        "order.customerId = :queryCustomerId",
        {
          queryCustomerId,
        }
      );
    }

    if (tableId) {
      queryBuilder = queryBuilder.andWhere("order.tableId = :tableId", {
        tableId,
      });
    }

    // Date filtering
    if (date) {
      const startDate = new Date(date);
      const endDate = new Date(date);
      endDate.setDate(endDate.getDate() + 1);

      queryBuilder = queryBuilder
        .andWhere("order.createdAt >= :startDate", {
          startDate,
        })
        .andWhere("order.createdAt < :endDate", { endDate });
    } else {
      if (fromDate) {
        queryBuilder = queryBuilder.andWhere("order.createdAt >= :fromDate", {
          fromDate: new Date(fromDate),
        });
      }

      if (toDate) {
        const endDate = new Date(toDate);
        endDate.setDate(endDate.getDate() + 1);
        queryBuilder = queryBuilder.andWhere("order.createdAt < :endDate", {
          endDate,
        });
      }
    }

    // Apply sorting
    const validSortFields = [
      "orderNumber",
      "status",
      "type",
      "totalAmount",
      "createdAt",
      "updatedAt",
    ];
    const sortField = validSortFields.includes(sortBy) ? sortBy : "createdAt";
    queryBuilder = queryBuilder.orderBy(
      `order.${sortField}`,
      sortOrder.toUpperCase() as "ASC" | "DESC"
    );

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder = queryBuilder.skip(offset).take(limit);

    // Execute query
    const [orders, total] = await queryBuilder.getManyAndCount();

    return {
      data: orders,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * Find order by ID with all relations
   */
  async findByIdWithRelations(id: string): Promise<Order | null> {
    return this.orderRepository.findOne({
      where: { id, deletedAt: null },
      relations: [
        "items",
        "items.product",
        "customer",
        "waiter",
        "table",
        "restaurant",
      ],
    });
  }

  /**
   * Find orders by status
   */
  async findByStatus(
    restaurantId: string,
    status: OrderStatus
  ): Promise<Order[]> {
    return this.orderRepository.find({
      where: {
        restaurantId,
        status,
        deletedAt: null,
      },
      relations: ["items", "items.product", "customer", "waiter", "table"],
      order: {
        createdAt: "ASC",
      },
    });
  }

  /**
   * Find orders by table
   */
  async findByTable(
    restaurantId: string,
    tableId: string,
    activeOnly: boolean = false
  ): Promise<Order[]> {
    const whereCondition: any = {
      restaurantId,
      tableId,
      deletedAt: null,
    };

    if (activeOnly) {
      whereCondition.status = In([
        OrderStatus.PENDING,
        OrderStatus.CONFIRMED,
        OrderStatus.PREPARING,
        OrderStatus.READY,
      ]);
    }

    return this.orderRepository.find({
      where: whereCondition,
      relations: ["items", "items.product", "customer", "waiter"],
      order: {
        createdAt: "DESC",
      },
    });
  }

  /**
   * Find orders by customer
   */
  async findByCustomer(
    customerId: string,
    limit: number = 20
  ): Promise<Order[]> {
    return this.orderRepository.find({
      where: {
        customerId,
        deletedAt: null,
      },
      relations: ["items", "items.product", "restaurant", "table"],
      order: {
        createdAt: "DESC",
      },
      take: limit,
    });
  }

  /**
   * Get order statistics
   */
  async getOrderStats(
    restaurantId: string,
    fromDate?: Date,
    toDate?: Date
  ): Promise<OrderStatsDto> {
    let queryBuilder = this.orderRepository
      .createQueryBuilder("order")
      .where("order.restaurantId = :restaurantId", { restaurantId })
      .andWhere("order.deletedAt IS NULL");

    if (fromDate) {
      queryBuilder = queryBuilder.andWhere("order.createdAt >= :fromDate", {
        fromDate,
      });
    }

    if (toDate) {
      queryBuilder = queryBuilder.andWhere("order.createdAt <= :toDate", {
        toDate,
      });
    }

    const [totalOrders, pendingOrders, completedOrders, cancelledOrders] =
      await Promise.all([
        queryBuilder.getCount(),
        queryBuilder
          .clone()
          .andWhere("order.status = :status", { status: OrderStatus.PENDING })
          .getCount(),
        queryBuilder
          .clone()
          .andWhere("order.status = :status", { status: OrderStatus.COMPLETED })
          .getCount(),
        queryBuilder
          .clone()
          .andWhere("order.status = :status", { status: OrderStatus.CANCELLED })
          .getCount(),
      ]);

    // Calculate revenue from completed orders
    const revenueResult = await queryBuilder
      .clone()
      .select("SUM(order.totalAmount)", "totalRevenue")
      .andWhere("order.status = :status", { status: OrderStatus.COMPLETED })
      .getRawOne();

    const totalRevenue = parseFloat(revenueResult?.totalRevenue || "0");
    const averageOrderValue =
      completedOrders > 0 ? totalRevenue / completedOrders : 0;

    return {
      totalOrders,
      pendingOrders,
      completedOrders,
      cancelledOrders,
      totalRevenue,
      averageOrderValue,
    };
  }

  /**
   * Generate next order number
   */
  async generateOrderNumber(restaurantId: string): Promise<string> {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, "");

    const lastOrder = await this.orderRepository
      .createQueryBuilder("order")
      .where("order.restaurantId = :restaurantId", { restaurantId })
      .andWhere("order.orderNumber LIKE :pattern", {
        pattern: `${dateStr}-%`,
      })
      .orderBy("order.orderNumber", "DESC")
      .getOne();

    let nextNumber = 1;
    if (lastOrder) {
      const lastNumber = parseInt(lastOrder.orderNumber.split("-")[1]);
      nextNumber = lastNumber + 1;
    }

    return `${dateStr}-${nextNumber.toString().padStart(4, "0")}`;
  }

  /**
   * Update order status
   */
  async updateStatus(
    id: string,
    status: OrderStatus,
    notes?: string
  ): Promise<void> {
    const updateData: any = { status, updatedAt: new Date() };

    if (notes) {
      updateData.statusNotes = notes;
    }

    // Set completion time for completed orders
    if (status === OrderStatus.COMPLETED) {
      updateData.completedAt = new Date();
    }

    await this.orderRepository.update(id, updateData);
  }

  /**
   * Assign waiter to order
   */
  async assignWaiter(id: string, waiterId: string): Promise<void> {
    await this.orderRepository.update(id, {
      waiterId,
      updatedAt: new Date(),
    });
  }

  /**
   * Get active orders count by restaurant
   */
  async getActiveOrdersCount(restaurantId: string): Promise<number> {
    return this.orderRepository.count({
      where: {
        restaurantId,
        status: In([
          OrderStatus.PENDING,
          OrderStatus.CONFIRMED,
          OrderStatus.PREPARING,
          OrderStatus.READY,
        ]),
        deletedAt: null,
      },
    });
  }

  /**
   * Get orders by waiter
   */
  async findByWaiter(
    waiterId: string,
    restaurantId: string,
    activeOnly: boolean = false
  ): Promise<Order[]> {
    const whereCondition: any = {
      waiterId,
      restaurantId,
      deletedAt: null,
    };

    if (activeOnly) {
      whereCondition.status = In([
        OrderStatus.PENDING,
        OrderStatus.CONFIRMED,
        OrderStatus.PREPARING,
        OrderStatus.READY,
      ]);
    }

    return this.orderRepository.find({
      where: whereCondition,
      relations: ["items", "items.product", "customer", "table"],
      order: {
        createdAt: "DESC",
      },
    });
  }
}
