import { Application } from "express";
import swaggerJsdoc from "swagger-jsdoc";
import swaggerUi from "swagger-ui-express";
import { env } from "./environment";

const options: swaggerJsdoc.Options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: env.swagger.title,
      version: env.swagger.version,
      description: env.swagger.description,
    },
    servers: [
      {
        url: `http://localhost:${env.port}`,
        description: "Development server",
      },
    ],
    components: {
      securitySchemes: {
        cookieAuth: {
          type: "apiKey",
          in: "cookie",
          name: env.jwt.cookieName,
          description: "JWT token stored in HTTP-only cookie",
        },
      },
    },
    security: [
      {
        cookieAuth: [],
      },
    ],
  },
  // Path to the API docs - Updated to match your file structure
  apis: [
    "./src/routes/*.ts",
    "./src/features/**/*.routes.ts",
    "./src/features/**/*.controller.ts",
    "./src/features/**/*.dto.ts",
  ],
};

const swaggerSpec = swaggerJsdoc(options);

export function setupSwagger(app: Application): void {
  // Swagger UI options
  const swaggerUiOptions = {
    explorer: true,
    customCss: ".swagger-ui .topbar { display: none }",
    customSiteTitle: env.swagger.title,
  };

  // Serve swagger docs at /api-docs
  app.use(
    "/api-docs",
    swaggerUi.serve,
    swaggerUi.setup(swaggerSpec, swaggerUiOptions)
  );

  // Serve swagger JSON at /api-docs.json
  app.get("/api-docs.json", (_req, res) => {
    res.setHeader("Content-Type", "application/json");
    res.send(swaggerSpec);
  });

  // Health check endpoint
  app.get("/", (_req, res) => {
    res.json({
      message: "QR Code Menu App API",
      version: env.swagger.version,
      documentation: "/api-docs",
      health: "OK",
    });
  });

  // Log swagger URL
  console.log(
    `📚 Swagger documentation available at: http://localhost:${env.port}/api-docs`
  );
}
