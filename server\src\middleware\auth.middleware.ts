import { Request, Response, NextFunction } from "express";
import { AuthService } from "../features/auth/auth.service";
import { ApiError } from "../utils/ApiError";
import { env } from "../config/environment";

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        fullName: string;
        isActive: boolean;
        role: string;
        restaurantId?: string;
        createdAt: Date;
        updatedAt: Date;
      };
      session?: {
        id: string;
        userId: string;
        token: string;
        expiresAt: Date;
        isActive: boolean;
        ipAddress: string;
        userAgent: string;
        createdAt: Date;
      };
    }
  }
}

export class AuthMiddleware {
  private static authService = new AuthService();

  /**
   * Middleware to authenticate JWT token from cookies
   */
  static authenticate = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      // Get token from cookie
      const token = req.cookies?.[env.jwt.cookieName];

      if (!token) {
        throw new ApiError(401, "Authentication required");
      }

      // Validate token and get user data
      const { user, session } = await AuthMiddleware.authService.validateToken(
        token
      );

      // Attach user and session to request object
      req.user = user;
      req.session = session;

      next();
    } catch (error) {
      next(error);
    }
  };

  /**
   * Optional authentication middleware - doesn't throw error if no token
   */
  static optionalAuthenticate = async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const token = req.cookies?.[env.jwt.cookieName];

      if (token) {
        try {
          const { user, session } =
            await AuthMiddleware.authService.validateToken(token);
          req.user = user;
          req.session = session;
        } catch (error) {
          // Silently ignore authentication errors for optional auth
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  };

  /**
   * Middleware to check if user is active
   */
  static requireActiveUser = (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    if (!req.user) {
      return next(new ApiError(401, "Authentication required"));
    }

    if (!req.user.isActive) {
      return next(new ApiError(403, "Account is deactivated"));
    }

    next();
  };
}
