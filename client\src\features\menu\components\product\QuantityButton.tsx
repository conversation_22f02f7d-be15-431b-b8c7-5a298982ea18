import { MinusIcon } from "../../../../assets/icons/component/MinusIcon";
import { PlusIcon } from "../../../../assets/icons/component/PlusIcon";

export default function QuantityButton({
  quantity,
  setQuantity,
}: {
  quantity: number;
  setQuantity: (quantity: number) => void;
}) {
  return (
    <div className="flex items-center space-x-4">
      <span className="text-secondary-600 font-medium">Quantity:</span>
      <div className="flex items-center space-x-2 bg-secondary-100 rounded-full p-1">
        <button
          onClick={() => setQuantity(Math.max(1, quantity - 1))}
          className="p-2 rounded-full bg-white text-secondary-600 hover:bg-secondary-50 transition-colors"
        >
          <MinusIcon />
        </button>
        <span className="font-bold w-8 text-center">{quantity}</span>
        <button
          onClick={() => setQuantity(quantity + 1)}
          className="p-2 rounded-full bg-white text-primary-500 hover:bg-primary-50 transition-colors"
        >
          <PlusIcon />
        </button>
      </div>
    </div>
  );
}
