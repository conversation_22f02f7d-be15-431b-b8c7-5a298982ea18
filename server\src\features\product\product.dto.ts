import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON>al,
  Is<PERSON><PERSON>ber,
  <PERSON>,
  <PERSON>,
  IsArray,
  IsBoolean,
  IsUUID,
  ValidateNested,
} from "class-validator";
import { Type, Transform } from "class-transformer";
// Note: Using custom swagger decorators since we're not using NestJS
export const ApiProperty =
  (options?: any) => (target: any, propertyKey: string) => {};
export const ApiPropertyOptional =
  (options?: any) => (target: any, propertyKey: string) => {};

export class NutritionInfoDto {
  @ApiProperty({ example: 180, description: "Calories per serving" })
  @IsNumber({}, { message: "Calories must be a number" })
  @Min(0, { message: "Calories must be non-negative" })
  calories: number;

  @ApiProperty({ example: "3g", description: "Protein content" })
  @IsString({ message: "Protein must be a string" })
  protein: string;

  @ApiProperty({ example: "24g", description: "Carbohydrates content" })
  @IsString({ message: "Carbs must be a string" })
  carbs: string;

  @ApiProperty({ example: "8g", description: "Fat content" })
  @IsString({ message: "Fat must be a string" })
  fat: string;
}

export class CustomizationOptionDto {
  @ApiProperty({ example: "Extra cheese", description: "Option name" })
  @IsNotEmpty({ message: "Option name is required" })
  @IsString({ message: "Option name must be a string" })
  name: string;

  @ApiProperty({
    example: 2.5,
    description: "Additional price for this option",
  })
  @IsNumber({}, { message: "Price must be a number" })
  @Min(0, { message: "Price must be non-negative" })
  price: number;
}

export class CustomizationCategoryDto {
  @ApiProperty({ example: "Toppings", description: "Category name" })
  @IsNotEmpty({ message: "Category name is required" })
  @IsString({ message: "Category name must be a string" })
  name: string;

  @ApiProperty({
    example: false,
    description: "Whether this category is required",
  })
  @IsBoolean({ message: "Required must be a boolean" })
  required: boolean;

  @ApiProperty({
    type: [CustomizationOptionDto],
    description: "Available options",
  })
  @IsArray({ message: "Options must be an array" })
  @ValidateNested({ each: true })
  @Type(() => CustomizationOptionDto)
  options: CustomizationOptionDto[];
}

export class CreateProductDto {
  @ApiProperty({ example: "Margherita Pizza", description: "Product name" })
  @IsNotEmpty({ message: "Product name is required" })
  @IsString({ message: "Product name must be a string" })
  name: string;

  @ApiPropertyOptional({
    example: "Classic pizza with tomato and mozzarella",
    description: "Product description",
  })
  @IsOptional()
  @IsString({ message: "Description must be a string" })
  description?: string;

  @ApiProperty({ example: 15.99, description: "Product price" })
  @IsNotEmpty({ message: "Price is required" })
  @IsNumber({}, { message: "Price must be a number" })
  @Min(0, { message: "Price must be non-negative" })
  price: number;

  @ApiPropertyOptional({
    example: "https://example.com/image.jpg",
    description: "Product image URL",
  })
  @IsOptional()
  @IsString({ message: "Image URL must be a string" })
  imageUrl?: string;

  @ApiPropertyOptional({
    example: ["Tomato", "Mozzarella", "Basil"],
    description: "List of ingredients",
  })
  @IsOptional()
  @IsArray({ message: "Ingredients must be an array" })
  @IsString({ each: true, message: "Each ingredient must be a string" })
  ingredients?: string[];

  @ApiPropertyOptional({
    type: NutritionInfoDto,
    description: "Nutrition information",
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => NutritionInfoDto)
  nutritionInfo?: NutritionInfoDto;

  @ApiPropertyOptional({
    example: true,
    description: "Whether the product is available",
  })
  @IsOptional()
  @IsBoolean({ message: "isAvailable must be a boolean" })
  isAvailable?: boolean;

  @ApiPropertyOptional({
    example: false,
    description: "Whether the product is featured",
  })
  @IsOptional()
  @IsBoolean({ message: "isFeatured must be a boolean" })
  isFeatured?: boolean;

  @ApiPropertyOptional({
    example: false,
    description: "Whether the product is customizable",
  })
  @IsOptional()
  @IsBoolean({ message: "isCustomizable must be a boolean" })
  isCustomizable?: boolean;

  @ApiPropertyOptional({
    type: [CustomizationCategoryDto],
    description: "Customization categories",
  })
  @IsOptional()
  @IsArray({ message: "Customization categories must be an array" })
  @ValidateNested({ each: true })
  @Type(() => CustomizationCategoryDto)
  customizationCategories?: CustomizationCategoryDto[];

  @ApiPropertyOptional({ example: 0, description: "Sort order for display" })
  @IsOptional()
  @IsNumber({}, { message: "Sort order must be a number" })
  @Min(0, { message: "Sort order must be non-negative" })
  sortOrder?: number;

  @ApiPropertyOptional({
    example: ["vegetarian", "popular"],
    description: "Product tags",
  })
  @IsOptional()
  @IsArray({ message: "Tags must be an array" })
  @IsString({ each: true, message: "Each tag must be a string" })
  tags?: string[];

  @ApiPropertyOptional({
    example: 15,
    description: "Preparation time in minutes",
  })
  @IsOptional()
  @IsNumber({}, { message: "Preparation time must be a number" })
  @Min(0, { message: "Preparation time must be non-negative" })
  preparationTimeMinutes?: number;

  @ApiProperty({ example: "uuid", description: "Category ID" })
  @IsNotEmpty({ message: "Category ID is required" })
  @IsUUID(4, { message: "Category ID must be a valid UUID" })
  categoryId: string;

  @ApiProperty({ example: "uuid", description: "Restaurant ID" })
  @IsNotEmpty({ message: "Restaurant ID is required" })
  @IsUUID(4, { message: "Restaurant ID must be a valid UUID" })
  restaurantId: string;
}

export class UpdateProductDto {
  @ApiPropertyOptional({
    example: "Margherita Pizza",
    description: "Product name",
  })
  @IsOptional()
  @IsString({ message: "Product name must be a string" })
  name?: string;

  @ApiPropertyOptional({
    example: "Classic pizza with tomato and mozzarella",
    description: "Product description",
  })
  @IsOptional()
  @IsString({ message: "Description must be a string" })
  description?: string;

  @ApiPropertyOptional({ example: 15.99, description: "Product price" })
  @IsOptional()
  @IsNumber({}, { message: "Price must be a number" })
  @Min(0, { message: "Price must be non-negative" })
  price?: number;

  @ApiPropertyOptional({
    example: "https://example.com/image.jpg",
    description: "Product image URL",
  })
  @IsOptional()
  @IsString({ message: "Image URL must be a string" })
  imageUrl?: string;

  @ApiPropertyOptional({
    example: ["Tomato", "Mozzarella", "Basil"],
    description: "List of ingredients",
  })
  @IsOptional()
  @IsArray({ message: "Ingredients must be an array" })
  @IsString({ each: true, message: "Each ingredient must be a string" })
  ingredients?: string[];

  @ApiPropertyOptional({
    type: NutritionInfoDto,
    description: "Nutrition information",
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => NutritionInfoDto)
  nutritionInfo?: NutritionInfoDto;

  @ApiPropertyOptional({
    example: true,
    description: "Whether the product is available",
  })
  @IsOptional()
  @IsBoolean({ message: "isAvailable must be a boolean" })
  isAvailable?: boolean;

  @ApiPropertyOptional({
    example: false,
    description: "Whether the product is featured",
  })
  @IsOptional()
  @IsBoolean({ message: "isFeatured must be a boolean" })
  isFeatured?: boolean;

  @ApiPropertyOptional({
    example: false,
    description: "Whether the product is customizable",
  })
  @IsOptional()
  @IsBoolean({ message: "isCustomizable must be a boolean" })
  isCustomizable?: boolean;

  @ApiPropertyOptional({
    type: [CustomizationCategoryDto],
    description: "Customization categories",
  })
  @IsOptional()
  @IsArray({ message: "Customization categories must be an array" })
  @ValidateNested({ each: true })
  @Type(() => CustomizationCategoryDto)
  customizationCategories?: CustomizationCategoryDto[];

  @ApiPropertyOptional({ example: 0, description: "Sort order for display" })
  @IsOptional()
  @IsNumber({}, { message: "Sort order must be a number" })
  @Min(0, { message: "Sort order must be non-negative" })
  sortOrder?: number;

  @ApiPropertyOptional({
    example: ["vegetarian", "popular"],
    description: "Product tags",
  })
  @IsOptional()
  @IsArray({ message: "Tags must be an array" })
  @IsString({ each: true, message: "Each tag must be a string" })
  tags?: string[];

  @ApiPropertyOptional({
    example: 15,
    description: "Preparation time in minutes",
  })
  @IsOptional()
  @IsNumber({}, { message: "Preparation time must be a number" })
  @Min(0, { message: "Preparation time must be non-negative" })
  preparationTimeMinutes?: number;

  @ApiPropertyOptional({ example: "uuid", description: "Category ID" })
  @IsOptional()
  @IsUUID(4, { message: "Category ID must be a valid UUID" })
  categoryId?: string;
}

export class ProductQueryDto {
  @ApiPropertyOptional({ example: 1, description: "Page number" })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: "Page must be a number" })
  @Min(1, { message: "Page must be at least 1" })
  page?: number = 1;

  @ApiPropertyOptional({ example: 10, description: "Items per page" })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: "Limit must be a number" })
  @Min(1, { message: "Limit must be at least 1" })
  @Max(100, { message: "Limit cannot exceed 100" })
  limit?: number = 10;

  @ApiPropertyOptional({ example: "pizza", description: "Search term" })
  @IsOptional()
  @IsString({ message: "Search must be a string" })
  search?: string;

  @ApiPropertyOptional({ example: "uuid", description: "Category ID" })
  @IsOptional()
  @IsUUID(4, { message: "Category ID must be a valid UUID" })
  categoryId?: string;

  @ApiPropertyOptional({ example: true, description: "Filter by availability" })
  @IsOptional()
  @Transform(({ value }) => value === "true")
  @IsBoolean({ message: "isAvailable must be a boolean" })
  isAvailable?: boolean;

  @ApiPropertyOptional({
    example: true,
    description: "Filter by featured products",
  })
  @IsOptional()
  @Transform(({ value }) => value === "true")
  @IsBoolean({ message: "isFeatured must be a boolean" })
  isFeatured?: boolean;

  @ApiPropertyOptional({ example: "name", description: "Sort by field" })
  @IsOptional()
  @IsString({ message: "Sort by must be a string" })
  sortBy?: string = "createdAt";

  @ApiPropertyOptional({ example: "asc", description: "Sort order" })
  @IsOptional()
  @IsString({ message: "Sort order must be a string" })
  sortOrder?: "asc" | "desc" = "desc";
}
