import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from "typeorm";
import { IsNotEmpty, IsString, IsOptional, IsEmail, IsPhoneNumber } from "class-validator";
import { User } from "../user/user.model";
import { Product } from "../product/product.model";
import { Table } from "../table/table.model";
import { Order } from "../order/order.model";

@Entity("restaurants")
@Index(["name"], { unique: true })
export class Restaurant {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ type: "varchar", length: 255, unique: true })
  @IsNotEmpty({ message: "Restaurant name is required" })
  @IsString({ message: "Restaurant name must be a string" })
  name: string;

  @Column({ type: "text", nullable: true })
  @IsOptional()
  @IsString({ message: "Description must be a string" })
  description?: string;

  @Column({ type: "varchar", length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: "Address must be a string" })
  address?: string;

  @Column({ type: "varchar", length: 20, nullable: true })
  @IsOptional()
  @IsPhoneNumber(null, { message: "Please provide a valid phone number" })
  phone?: string;

  @Column({ type: "varchar", length: 255, nullable: true })
  @IsOptional()
  @IsEmail({}, { message: "Please provide a valid email address" })
  email?: string;

  @Column({ type: "varchar", length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: "Logo URL must be a string" })
  logoUrl?: string;

  @Column({ type: "varchar", length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: "Cover image URL must be a string" })
  coverImageUrl?: string;

  @Column({ type: "boolean", default: true })
  isActive: boolean;

  @Column({ type: "json", nullable: true })
  settings?: {
    currency: string;
    timezone: string;
    language: string;
    taxRate?: number;
    serviceCharge?: number;
  };

  @Column({ type: "json", nullable: true })
  businessHours?: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };

  // Relationships
  @Column({ type: "uuid" })
  ownerId: string;

  @ManyToOne(() => User, (user) => user.ownedRestaurants)
  @JoinColumn({ name: "ownerId" })
  owner: User;

  @OneToMany(() => User, (user) => user.restaurant)
  staff: User[];

  @OneToMany(() => Product, (product) => product.restaurant)
  products: Product[];

  @OneToMany(() => Table, (table) => table.restaurant)
  tables: Table[];

  @OneToMany(() => Order, (order) => order.restaurant)
  orders: Order[];

  // Timestamps
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  // Virtual properties
  get totalTables(): number {
    return this.tables?.length || 0;
  }

  get totalProducts(): number {
    return this.products?.length || 0;
  }

  get totalStaff(): number {
    return this.staff?.length || 0;
  }
}
