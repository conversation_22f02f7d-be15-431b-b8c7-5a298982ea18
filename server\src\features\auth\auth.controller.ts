import { Request, Response } from "express";
import { AuthService } from "./auth.service";
import { RegisterDto, LoginDto } from "./auth.dto";
import { BaseController } from "../../base/BaseController";
import { env } from "../../config/environment";

export class AuthController extends BaseController {
  protected controllerName = "AuthController";
  private authService: AuthService;

  constructor() {
    super();
    this.authService = new AuthService();
  }

  /**
   * Register a new user
   */
  register = this.asyncHandler(async (req: Request, res: Response) => {
    const registerData: RegisterDto = req.body;
    const ipAddress = req.ip || req.socket.remoteAddress;
    const userAgent = req.get("User-Agent");

    const result = await this.authService.register(
      registerData,
      ipAddress,
      userAgent
    );

    // Set JWT cookie
    const token = await this.authService.login(
      { email: registerData.email, password: registerData.password },
      ipAddress,
      userAgent
    );

    res.cookie(env.jwt.cookieName, token.token, {
      maxAge: env.cookie.maxAge,
      httpOnly: env.cookie.httpOnly,
      secure: env.cookie.secure,
      sameSite: env.cookie.sameSite,
    });

    this.sendResponse(res, 201, result, "User registered successfully");
  });

  /**
   * Login user
   */
  login = this.asyncHandler(async (req: Request, res: Response) => {
    const loginData: LoginDto = req.body;
    const ipAddress = req.ip || req.socket.remoteAddress;
    const userAgent = req.get("User-Agent");

    const result = await this.authService.login(
      loginData,
      ipAddress,
      userAgent
    );

    // Set JWT cookie
    res.cookie(env.jwt.cookieName, result.token, {
      maxAge: env.cookie.maxAge,
      httpOnly: env.cookie.httpOnly,
      secure: env.cookie.secure,
      sameSite: env.cookie.sameSite,
    });

    // Remove token from response for security
    const { token, ...responseData } = result;

    this.sendResponse(res, 200, responseData, "Login successful");
  });

  /**
   * Logout user
   */
  logout = this.asyncHandler(async (req: Request, res: Response) => {
    const token = req.cookies?.[env.jwt.cookieName];

    if (token) {
      await this.authService.logout(token);
    }

    // Clear cookie
    res.clearCookie(env.jwt.cookieName);

    this.sendResponse(res, 200, null, "Logout successful");
  });

  /**
   * Get current user profile
   */
  getProfile = this.asyncHandler(async (req: Request, res: Response) => {
    const user = req.user;

    this.sendResponse(res, 200, user, "Profile retrieved successfully");
  });

  /**
   * Logout from all sessions
   */
  logoutAll = this.asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.id;

    if (userId) {
      await this.authService.logoutAllSessions(userId);
    }

    // Clear cookie
    res.clearCookie(env.jwt.cookieName);

    this.sendResponse(
      res,
      200,
      null,
      "Logged out from all sessions successfully"
    );
  });
}
