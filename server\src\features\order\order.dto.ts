import { 
  IsNotEmpty, 
  IsString, 
  IsOptional, 
  IsNumber, 
  Min, 
  IsArray, 
  IsBoolean, 
  IsUUID,
  ValidateNested,
  IsEnum,
  IsObject
} from "class-validator";
import { Type, Transform } from "class-transformer";
import { OrderStatus, OrderType } from "./order.model";

// Note: Using custom swagger decorators since we're not using NestJS
export const ApiProperty = (options?: any) => (target: any, propertyKey: string) => {};
export const ApiPropertyOptional = (options?: any) => (target: any, propertyKey: string) => {};

export class ItemCustomizationDto {
  @ApiProperty({ example: { "Café": { "Espresso": 1 }, "Jus": { "Orange": 1 } }, description: "Customization selections" })
  @IsObject({ message: "Customizations must be an object" })
  [category: string]: {
    [itemName: string]: number;
  };
}

export class CreateOrderItemDto {
  @ApiProperty({ example: "uuid", description: "Product ID" })
  @IsNotEmpty({ message: "Product ID is required" })
  @IsUUID(4, { message: "Product ID must be a valid UUID" })
  productId: string;

  @ApiProperty({ example: 2, description: "Quantity" })
  @IsNotEmpty({ message: "Quantity is required" })
  @IsNumber({}, { message: "Quantity must be a number" })
  @Min(1, { message: "Quantity must be at least 1" })
  quantity: number;

  @ApiPropertyOptional({ type: ItemCustomizationDto, description: "Product customizations" })
  @IsOptional()
  @IsObject({ message: "Customizations must be an object" })
  customizations?: { [category: string]: { [itemName: string]: number } };

  @ApiPropertyOptional({ example: "No onions please", description: "Special instructions" })
  @IsOptional()
  @IsString({ message: "Special instructions must be a string" })
  specialInstructions?: string;
}

export class CreateOrderDto {
  @ApiProperty({ example: "uuid", description: "Restaurant ID" })
  @IsNotEmpty({ message: "Restaurant ID is required" })
  @IsUUID(4, { message: "Restaurant ID must be a valid UUID" })
  restaurantId: string;

  @ApiPropertyOptional({ example: "uuid", description: "Table ID for dine-in orders" })
  @IsOptional()
  @IsUUID(4, { message: "Table ID must be a valid UUID" })
  tableId?: string;

  @ApiProperty({ 
    example: "DINE_IN", 
    enum: OrderType,
    description: "Order type" 
  })
  @IsNotEmpty({ message: "Order type is required" })
  @IsEnum(OrderType, { message: "Invalid order type" })
  type: OrderType;

  @ApiProperty({ type: [CreateOrderItemDto], description: "Order items" })
  @IsNotEmpty({ message: "Order items are required" })
  @IsArray({ message: "Order items must be an array" })
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  items: CreateOrderItemDto[];

  @ApiPropertyOptional({ example: "Please prepare quickly", description: "Order notes" })
  @IsOptional()
  @IsString({ message: "Notes must be a string" })
  notes?: string;

  // Delivery information (for delivery orders)
  @ApiPropertyOptional({ example: "123 Main St, City", description: "Delivery address" })
  @IsOptional()
  @IsString({ message: "Delivery address must be a string" })
  deliveryAddress?: string;

  @ApiPropertyOptional({ example: "+1234567890", description: "Delivery phone" })
  @IsOptional()
  @IsString({ message: "Delivery phone must be a string" })
  deliveryPhone?: string;

  @ApiPropertyOptional({ example: "John Doe", description: "Customer name for delivery" })
  @IsOptional()
  @IsString({ message: "Customer name must be a string" })
  customerName?: string;
}

export class UpdateOrderStatusDto {
  @ApiProperty({ 
    example: "CONFIRMED", 
    enum: OrderStatus,
    description: "New order status" 
  })
  @IsNotEmpty({ message: "Status is required" })
  @IsEnum(OrderStatus, { message: "Invalid order status" })
  status: OrderStatus;

  @ApiPropertyOptional({ example: "Order is being prepared", description: "Status update notes" })
  @IsOptional()
  @IsString({ message: "Notes must be a string" })
  notes?: string;
}

export class OrderQueryDto {
  @ApiPropertyOptional({ example: 1, description: "Page number" })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: "Page must be a number" })
  @Min(1, { message: "Page must be at least 1" })
  page?: number = 1;

  @ApiPropertyOptional({ example: 10, description: "Items per page" })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: "Limit must be a number" })
  @Min(1, { message: "Limit must be at least 1" })
  limit?: number = 10;

  @ApiPropertyOptional({ 
    example: "PENDING", 
    enum: OrderStatus,
    description: "Filter by status" 
  })
  @IsOptional()
  @IsEnum(OrderStatus, { message: "Invalid order status" })
  status?: OrderStatus;

  @ApiPropertyOptional({ 
    example: "DINE_IN", 
    enum: OrderType,
    description: "Filter by type" 
  })
  @IsOptional()
  @IsEnum(OrderType, { message: "Invalid order type" })
  type?: OrderType;

  @ApiPropertyOptional({ example: "uuid", description: "Filter by customer ID" })
  @IsOptional()
  @IsUUID(4, { message: "Customer ID must be a valid UUID" })
  customerId?: string;

  @ApiPropertyOptional({ example: "uuid", description: "Filter by table ID" })
  @IsOptional()
  @IsUUID(4, { message: "Table ID must be a valid UUID" })
  tableId?: string;

  @ApiPropertyOptional({ example: "2024-01-01", description: "Filter by date (YYYY-MM-DD)" })
  @IsOptional()
  @IsString({ message: "Date must be a string" })
  date?: string;

  @ApiPropertyOptional({ example: "2024-01-01", description: "Filter from date (YYYY-MM-DD)" })
  @IsOptional()
  @IsString({ message: "From date must be a string" })
  fromDate?: string;

  @ApiPropertyOptional({ example: "2024-01-31", description: "Filter to date (YYYY-MM-DD)" })
  @IsOptional()
  @IsString({ message: "To date must be a string" })
  toDate?: string;

  @ApiPropertyOptional({ example: "createdAt", description: "Sort by field" })
  @IsOptional()
  @IsString({ message: "Sort by must be a string" })
  sortBy?: string = "createdAt";

  @ApiPropertyOptional({ example: "desc", description: "Sort order" })
  @IsOptional()
  @IsString({ message: "Sort order must be a string" })
  sortOrder?: "asc" | "desc" = "desc";
}

export class AssignWaiterDto {
  @ApiProperty({ example: "uuid", description: "Waiter user ID" })
  @IsNotEmpty({ message: "Waiter ID is required" })
  @IsUUID(4, { message: "Waiter ID must be a valid UUID" })
  waiterId: string;
}

export class OrderStatsDto {
  @ApiProperty({ example: 150, description: "Total orders" })
  totalOrders: number;

  @ApiProperty({ example: 25, description: "Pending orders" })
  pendingOrders: number;

  @ApiProperty({ example: 100, description: "Completed orders" })
  completedOrders: number;

  @ApiProperty({ example: 5, description: "Cancelled orders" })
  cancelledOrders: number;

  @ApiProperty({ example: 2500.50, description: "Total revenue" })
  totalRevenue: number;

  @ApiProperty({ example: 16.67, description: "Average order value" })
  averageOrderValue: number;
}

export class OrderSummaryDto {
  @ApiProperty({ example: "uuid", description: "Order ID" })
  id: string;

  @ApiProperty({ example: "ORD-001", description: "Order number" })
  orderNumber: string;

  @ApiProperty({ example: "PENDING", enum: OrderStatus, description: "Order status" })
  status: OrderStatus;

  @ApiProperty({ example: "DINE_IN", enum: OrderType, description: "Order type" })
  type: OrderType;

  @ApiProperty({ example: 25.50, description: "Total amount" })
  totalAmount: number;

  @ApiProperty({ example: 3, description: "Number of items" })
  itemsCount: number;

  @ApiProperty({ example: "John Doe", description: "Customer name" })
  customerName: string;

  @ApiProperty({ example: "Table 5", description: "Table name" })
  tableName?: string;

  @ApiProperty({ example: "2024-01-15T10:30:00Z", description: "Order creation time" })
  createdAt: Date;

  @ApiProperty({ example: "2024-01-15T11:00:00Z", description: "Estimated completion time" })
  estimatedCompletionTime?: Date;
}
